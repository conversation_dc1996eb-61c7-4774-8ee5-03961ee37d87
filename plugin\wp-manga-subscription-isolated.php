<?php
/**
 *  Plugin Name: WP Manga - Subscription System (Isolated)
 *  Description: Pure subscription system - completely isolated from manga editing
 *  Plugin URI: https://www.mangabooth.com/
 *  Author: MangaBooth
 *  Version: 1.0.0-isolated
 *  Text Domain: wp-manga-subscription
 * @since 1.0
 *
 * @required - WP Manga Core *******
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

// Define constants
if (!defined('WP_MANGA_SUBSCRIPTION_FILE')) {
    define('WP_MANGA_SUBSCRIPTION_FILE', __FILE__);
}

if (!defined('WP_MANGA_SUBSCRIPTION_URI')) {
    define('WP_MANGA_SUBSCRIPTION_URI', plugin_dir_url(__FILE__));
}

if (!defined('WP_MANGA_SUBSCRIPTION_DIR')) {
    define('WP_MANGA_SUBSCRIPTION_DIR', plugin_dir_path(__FILE__));
}

define('WP_MANGA_SUBSCRIPTION_TEXT_DOMAIN', 'wp-manga-subscription');

// Check if WP Manga Core is available
if (!class_exists('WP_MANGA')) {
    add_action('admin_notices', function() {
        echo '<div class="notice notice-error is-dismissible">';
        echo '<p><strong>WP Manga - Subscription System:</strong> This plugin requires WP Manga Core to be installed and activated.</p>';
        echo '</div>';
    });
    return;
}

// Include only the subscription manager (no backend interference)
$subscription_manager_file = WP_MANGA_SUBSCRIPTION_DIR . 'admin/subscription-manager.php';
if (file_exists($subscription_manager_file)) {
    require_once($subscription_manager_file);
}

/**
 * Isolated subscription plugin class
 */
class WP_MANGA_SUBSCRIPTION_ISOLATED {
    private static $instance;

    public static function get_instance() {
        if (null == self::$instance) {
            self::$instance = new WP_MANGA_SUBSCRIPTION_ISOLATED();
        }
        return self::$instance;
    }

    private function __construct() {
        add_action('init', array($this, 'init'));
        register_activation_hook(__FILE__, array($this, 'activate'));
    }

    public function init() {
        try {
            // Initialize database if needed
            $this->init_database();
            
            // Add ONLY subscription-related hooks (no admin interference)
            $this->add_subscription_hooks();
            
            // Add admin menu
            add_action('admin_menu', array($this, 'add_admin_menu'));
            
        } catch (Exception $e) {
            error_log('WP Manga Subscription Init Error: ' . $e->getMessage());
        }
    }

    public function activate() {
        try {
            // Create subscription tables
            $this->create_subscription_tables();
            
            // Set activation notice
            set_transient('wp_manga_subscription_isolated_activated', true, 60);
            
        } catch (Exception $e) {
            error_log('WP Manga Subscription Activation Error: ' . $e->getMessage());
        }
    }

    private function create_subscription_tables() {
        global $wpdb;
        
        $charset_collate = $wpdb->get_charset_collate();
        
        // Subscription plans table
        $plans_table = $wpdb->prefix . 'manga_subscription_plans';
        $plans_sql = "CREATE TABLE $plans_table (
            plan_id int(11) NOT NULL AUTO_INCREMENT,
            plan_name varchar(255) NOT NULL,
            duration_months int(11) NOT NULL,
            price decimal(10,2) NOT NULL,
            is_active tinyint(1) DEFAULT 1,
            created_at datetime DEFAULT CURRENT_TIMESTAMP,
            PRIMARY KEY (plan_id)
        ) $charset_collate;";
        
        // User subscriptions table
        $subscriptions_table = $wpdb->prefix . 'manga_user_subscriptions';
        $subscriptions_sql = "CREATE TABLE $subscriptions_table (
            subscription_id int(11) NOT NULL AUTO_INCREMENT,
            user_id int(11) NOT NULL,
            plan_id int(11) NOT NULL,
            start_date datetime NOT NULL,
            end_date datetime NOT NULL,
            status enum('active','expired','cancelled') DEFAULT 'active',
            created_at datetime DEFAULT CURRENT_TIMESTAMP,
            PRIMARY KEY (subscription_id),
            KEY user_id (user_id),
            KEY plan_id (plan_id)
        ) $charset_collate;";
        
        require_once(ABSPATH . 'wp-admin/includes/upgrade.php');
        dbDelta($plans_sql);
        dbDelta($subscriptions_sql);
        
        // Insert default plans
        $this->insert_default_plans();
        
        // Update database version
        update_option('wp_manga_subscription_db_ver', '1.0.0');
    }

    private function insert_default_plans() {
        global $wpdb;
        
        $plans_table = $wpdb->prefix . 'manga_subscription_plans';
        
        // Check if plans already exist
        $existing_plans = $wpdb->get_var("SELECT COUNT(*) FROM $plans_table");
        
        if ($existing_plans == 0) {
            $default_plans = array(
                array('6-Month Premium', 6, 29.99),
                array('12-Month Premium', 12, 49.99)
            );
            
            foreach ($default_plans as $plan) {
                $wpdb->insert(
                    $plans_table,
                    array(
                        'plan_name' => $plan[0],
                        'duration_months' => $plan[1],
                        'price' => $plan[2],
                        'is_active' => 1
                    ),
                    array('%s', '%d', '%f', '%d')
                );
            }
        }
    }

    private function init_database() {
        if (get_option('wp_manga_subscription_db_ver', '') == '') {
            $this->create_subscription_tables();
        }
    }

    private function add_subscription_hooks() {
        // ONLY add hooks that don't interfere with admin

        // Frontend user menu
        add_filter('wp_manga_user_menu_before_items', array($this, 'add_user_menu_item'));
        add_action('wp_manga_user_settings_tab_nav', array($this, 'user_settings_tab_nav'), 10, 2);
        add_action('wp_manga_user_settings_tab_content', array($this, 'user_settings_tab_content'), 10, 2);

        // AJAX for subscription management (frontend only)
        add_action('wp_ajax_create_subscription_payment', array($this, 'ajax_create_payment'));
        add_action('wp_ajax_cancel_subscription', array($this, 'ajax_cancel_subscription'));
        add_action('wp_ajax_process_paypal_return', array($this, 'ajax_process_paypal_return'));

        // AJAX for admin chapter premium setting
        add_action('wp_ajax_save_chapter_premium_setting', array($this, 'ajax_save_chapter_premium_setting'));

        // Chapter access control (frontend only)
        add_action('wp_manga_chapter_content_alternative', array($this, 'display_subscription_required_message'));
        add_filter('wp_manga_chapter_content', array($this, 'filter_chapter_content'), 10, 2);

        // Add subscription modal to footer
        add_action('wp_footer', array($this, 'add_subscription_modal'));

        // Add admin scripts for chapter premium setting
        add_action('admin_enqueue_scripts', array($this, 'enqueue_admin_scripts'));

        // Add shortcodes
        add_shortcode('manga_subscription_page', array($this, 'subscription_page_shortcode'));
        add_shortcode('manga_subscribe_button', array($this, 'subscribe_button_shortcode'));

        // Add compatibility with child theme premium chapters
        add_action('wp_enqueue_scripts', array($this, 'enqueue_child_theme_compatibility'), 20);

        // Add chapter premium setting
        add_action('madara_chapter_modal_after_chapter_meta', array($this, 'add_chapter_premium_setting'), 10, 2);
        add_action('wp_manga_before_admin_save_chapter', array($this, 'save_chapter_premium_setting_ajax'), 10, 2);

        // Add premium badges and chapter access control
        add_filter('wp_manga_chapter_item_class', array($this, 'add_premium_chapter_class'), 10, 3);
        add_action('wp_manga_chapter_before_name', array($this, 'add_premium_badge'), 10, 2);
        add_action('wp_enqueue_scripts', array($this, 'enqueue_frontend_scripts'));

        // Block premium chapter content completely
        add_filter('wp_manga_chapter_images_data', array($this, 'filter_premium_chapter_images'), 10, 1);
    }

    public function add_admin_menu() {
        add_submenu_page(
            'edit.php?post_type=wp-manga',
            'Subscription System',
            'Subscriptions',
            'manage_options',
            'manga-subscriptions',
            array($this, 'admin_page')
        );
    }

    public function admin_page() {
        // Handle form submissions
        if (isset($_POST['save_paypal_settings'])) {
            $this->save_paypal_settings();
        }

        if (isset($_POST['bulk_set_premium'])) {
            $this->handle_bulk_premium_setting();
        }

        ?>
        <div class="wrap">
            <h1>🎯 Manga Subscription System</h1>

            <div class="notice notice-info">
                <p><strong>How to use this plugin:</strong></p>
                <ol>
                    <li><strong>Configure PayPal:</strong> Set up PayPal credentials below</li>
                    <li><strong>Set Premium Chapters:</strong> Use the bulk tool below or edit individual chapters</li>
                    <li><strong>Create Subscription Page:</strong> Use shortcode [manga_subscription_page]</li>
                    <li><strong>For Users:</strong> Users can subscribe via the subscription page or modal</li>
                </ol>
            </div>

            <?php $this->display_paypal_settings(); ?>
            <?php $this->display_chapter_management(); ?>
            <?php $this->display_subscription_plans(); ?>
            <?php $this->display_subscription_stats(); ?>
            <?php $this->display_usage_guide(); ?>
        </div>
        <?php
    }

    private function save_paypal_settings() {
        if (!current_user_can('manage_options')) {
            return;
        }

        update_option('manga_subscription_paypal_client_id', sanitize_text_field($_POST['paypal_client_id']));
        update_option('manga_subscription_paypal_client_secret', sanitize_text_field($_POST['paypal_client_secret']));
        update_option('manga_subscription_paypal_mode', sanitize_text_field($_POST['paypal_mode']));

        echo '<div class="notice notice-success"><p>PayPal settings saved successfully!</p></div>';
    }

    private function display_paypal_settings() {
        $client_id = get_option('manga_subscription_paypal_client_id', '');
        $client_secret = get_option('manga_subscription_paypal_client_secret', '');
        $mode = get_option('manga_subscription_paypal_mode', 'sandbox');

        ?>
        <div class="card">
            <h2>💳 PayPal Configuration</h2>
            <form method="post" action="">
                <table class="form-table">
                    <tr>
                        <th scope="row">PayPal Mode</th>
                        <td>
                            <select name="paypal_mode">
                                <option value="sandbox" <?php selected($mode, 'sandbox'); ?>>Sandbox (Testing)</option>
                                <option value="live" <?php selected($mode, 'live'); ?>>Live (Production)</option>
                            </select>
                            <p class="description">Use Sandbox for testing, Live for production</p>
                        </td>
                    </tr>
                    <tr>
                        <th scope="row">Client ID</th>
                        <td>
                            <input type="text" name="paypal_client_id" value="<?php echo esc_attr($client_id); ?>" class="regular-text" />
                            <p class="description">Get this from your PayPal Developer Dashboard</p>
                        </td>
                    </tr>
                    <tr>
                        <th scope="row">Client Secret</th>
                        <td>
                            <input type="password" name="paypal_client_secret" value="<?php echo esc_attr($client_secret); ?>" class="regular-text" />
                            <p class="description">Get this from your PayPal Developer Dashboard</p>
                        </td>
                    </tr>
                </table>
                <?php submit_button('Save PayPal Settings', 'primary', 'save_paypal_settings'); ?>
            </form>

            <?php if (empty($client_id) || empty($client_secret)): ?>
                <div class="notice notice-warning inline">
                    <p><strong>⚠️ PayPal not configured!</strong> Subscriptions won't work until you configure PayPal credentials.</p>
                    <p><strong>Setup Instructions:</strong></p>
                    <ol>
                        <li>Go to <a href="https://developer.paypal.com/" target="_blank">PayPal Developer Dashboard</a></li>
                        <li>Create a new app or use existing one</li>
                        <li>Copy the Client ID and Client Secret</li>
                        <li>Paste them in the fields above</li>
                        <li>Start with Sandbox mode for testing</li>
                    </ol>
                </div>
            <?php else: ?>
                <div class="notice notice-success inline">
                    <p><strong>✅ PayPal configured!</strong> Subscriptions are ready to work.</p>
                </div>
            <?php endif; ?>
        </div>
        <?php
    }

    private function display_subscription_plans() {
        global $wpdb;
        $plans_table = $wpdb->prefix . 'manga_subscription_plans';
        $plans = $wpdb->get_results("SELECT * FROM $plans_table ORDER BY duration_months ASC");
        
        ?>
        <div class="card">
            <h2>📋 Subscription Plans</h2>
            <?php if (!empty($plans)): ?>
                <table class="widefat">
                    <thead>
                        <tr>
                            <th>Plan Name</th>
                            <th>Duration</th>
                            <th>Price</th>
                            <th>Status</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($plans as $plan): ?>
                        <tr>
                            <td><strong><?php echo esc_html($plan->plan_name); ?></strong></td>
                            <td><?php echo esc_html($plan->duration_months); ?> months</td>
                            <td>$<?php echo number_format($plan->price, 2); ?></td>
                            <td>
                                <span class="<?php echo $plan->is_active ? 'dashicons dashicons-yes-alt' : 'dashicons dashicons-dismiss'; ?>" style="color: <?php echo $plan->is_active ? 'green' : 'red'; ?>;"></span>
                                <?php echo $plan->is_active ? 'Active' : 'Inactive'; ?>
                            </td>
                            <td>
                                <button type="button" class="button button-small" onclick="togglePlan(<?php echo $plan->plan_id; ?>, <?php echo $plan->is_active; ?>)">
                                    <?php echo $plan->is_active ? 'Deactivate' : 'Activate'; ?>
                                </button>
                            </td>
                        </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            <?php else: ?>
                <p>No subscription plans found. <button type="button" class="button" onclick="createDefaultPlans()">Create Default Plans</button></p>
            <?php endif; ?>
        </div>
        <?php
    }

    private function display_subscription_stats() {
        global $wpdb;
        $subscriptions_table = $wpdb->prefix . 'manga_user_subscriptions';
        
        $stats = array(
            'active' => $wpdb->get_var("SELECT COUNT(*) FROM $subscriptions_table WHERE status = 'active'"),
            'expired' => $wpdb->get_var("SELECT COUNT(*) FROM $subscriptions_table WHERE status = 'expired'"),
            'cancelled' => $wpdb->get_var("SELECT COUNT(*) FROM $subscriptions_table WHERE status = 'cancelled'"),
            'total' => $wpdb->get_var("SELECT COUNT(*) FROM $subscriptions_table")
        );
        
        ?>
        <div class="card">
            <h2>📊 Subscription Statistics</h2>
            <div style="display: grid; grid-template-columns: repeat(4, 1fr); gap: 20px;">
                <div style="text-align: center; padding: 20px; background: #e7f3ff; border-radius: 5px;">
                    <h3 style="margin: 0; color: #0073aa;"><?php echo $stats['active']; ?></h3>
                    <p style="margin: 5px 0 0 0;">Active</p>
                </div>
                <div style="text-align: center; padding: 20px; background: #fff2e7; border-radius: 5px;">
                    <h3 style="margin: 0; color: #d63638;"><?php echo $stats['expired']; ?></h3>
                    <p style="margin: 5px 0 0 0;">Expired</p>
                </div>
                <div style="text-align: center; padding: 20px; background: #f0f0f0; border-radius: 5px;">
                    <h3 style="margin: 0; color: #646970;"><?php echo $stats['cancelled']; ?></h3>
                    <p style="margin: 5px 0 0 0;">Cancelled</p>
                </div>
                <div style="text-align: center; padding: 20px; background: #e7f7e7; border-radius: 5px;">
                    <h3 style="margin: 0; color: #00a32a;"><?php echo $stats['total']; ?></h3>
                    <p style="margin: 5px 0 0 0;">Total</p>
                </div>
            </div>
        </div>
        <?php
    }

    private function display_usage_guide() {
        ?>
        <div class="card">
            <h2>📖 How to Use the Subscription System</h2>
            
            <h3>For Website Visitors:</h3>
            <ol>
                <li><strong>Create Account:</strong> Users need to register/login to your site</li>
                <li><strong>View Subscription:</strong> Go to Account → "My Subscription" tab</li>
                <li><strong>Subscribe:</strong> Choose a plan and complete payment</li>
                <li><strong>Access Premium:</strong> Premium chapters become accessible</li>
            </ol>
            
            <h3>For Administrators:</h3>
            <ol>
                <li><strong>Manage Plans:</strong> Use this page to activate/deactivate plans</li>
                <li><strong>View Statistics:</strong> Monitor subscription numbers above</li>
                <li><strong>Chapter Settings:</strong> Mark chapters as premium in chapter editor</li>
                <li><strong>User Management:</strong> Manually manage subscriptions if needed</li>
            </ol>
            
            <h3>Next Steps to Complete Setup:</h3>
            <ul>
                <li>✅ <strong>Database:</strong> Subscription tables created</li>
                <li>✅ <strong>Plans:</strong> Default plans available</li>
                <li>⚠️ <strong>Payment:</strong> Integrate payment gateway (PayPal, Stripe, etc.)</li>
                <li>⚠️ <strong>Chapter Access:</strong> Configure which chapters require subscription</li>
                <li>⚠️ <strong>Styling:</strong> Customize subscription interface appearance</li>
            </ul>
        </div>
        
        <script>
        function togglePlan(planId, currentStatus) {
            alert('Plan toggle functionality will be implemented here. Plan ID: ' + planId);
        }
        
        function createDefaultPlans() {
            alert('Default plans creation will be implemented here.');
        }
        </script>
        <?php
    }

    // Frontend functionality for user integration
    public function add_user_menu_item($html) {
        // Add subscription menu item to user account
        $subscription_url = home_url('/subscription/');
        $user_id = get_current_user_id();
        $has_subscription = $user_id ? $this->user_has_active_subscription($user_id) : false;

        $status_icon = $has_subscription ? '👑' : '🔒';
        $status_text = $has_subscription ? 'Premium Member' : 'Subscribe';

        $html .= '<li class="menu-item-subscription">';
        $html .= '<a href="' . esc_url($subscription_url) . '" class="subscription-menu-link">';
        $html .= '<span class="subscription-icon">' . $status_icon . '</span>';
        $html .= '<span class="subscription-text">' . esc_html($status_text) . '</span>';
        $html .= '</a>';
        $html .= '</li>';

        return $html;
    }

    public function user_settings_tab_nav($tab_pane, $account) {
        // Add subscription tab to user settings
        if ($tab_pane === 'subscription' || $tab_pane === 'my-subscription') {
            echo '<li class="nav-item">';
            echo '<a class="nav-link" href="#subscription" data-toggle="tab">👑 My Subscription</a>';
            echo '</li>';
        }
    }

    public function user_settings_tab_content($tab_pane, $account) {
        // Display subscription content in user settings
        if ($tab_pane === 'subscription' || $tab_pane === 'my-subscription') {
            $this->display_user_subscription_content();
        }
    }

    private function display_user_subscription_content() {
        $user_id = get_current_user_id();
        $subscription = $this->get_user_subscription_details($user_id);

        ?>
        <div class="user-subscription-content">
            <h3>👑 Premium Subscription</h3>

            <?php if ($subscription): ?>
                <div class="subscription-active">
                    <div class="subscription-status">
                        <span class="status-badge active">✅ Active</span>
                        <h4><?php echo esc_html($subscription['plan_name']); ?></h4>
                    </div>

                    <div class="subscription-details">
                        <div class="detail-item">
                            <strong>Start Date:</strong>
                            <span><?php echo date('F j, Y', strtotime($subscription['start_date'])); ?></span>
                        </div>
                        <div class="detail-item">
                            <strong>End Date:</strong>
                            <span><?php echo date('F j, Y', strtotime($subscription['end_date'])); ?></span>
                        </div>
                        <div class="detail-item">
                            <strong>Status:</strong>
                            <span class="status-<?php echo esc_attr($subscription['status']); ?>">
                                <?php echo ucfirst($subscription['status']); ?>
                            </span>
                        </div>
                    </div>

                    <div class="subscription-actions">
                        <button class="btn btn-danger" onclick="cancelSubscription()">
                            Cancel Subscription
                        </button>
                    </div>
                </div>
            <?php else: ?>
                <div class="subscription-inactive">
                    <div class="no-subscription-message">
                        <h4>🔒 No Active Subscription</h4>
                        <p>Subscribe to access premium manga chapters and exclusive content.</p>

                        <div class="subscription-benefits">
                            <h5>Premium Benefits:</h5>
                            <ul>
                                <li>✅ Unlimited access to premium chapters</li>
                                <li>✅ Early access to new releases</li>
                                <li>✅ Ad-free reading experience</li>
                                <li>✅ HD quality images</li>
                            </ul>
                        </div>

                        <div class="subscription-cta">
                            <button class="btn btn-primary" onclick="openSubscriptionModal()">
                                Subscribe Now
                            </button>
                        </div>
                    </div>
                </div>
            <?php endif; ?>
        </div>

        <style>
        .user-subscription-content {
            padding: 20px;
            background: #f8f9fa;
            border-radius: 8px;
            margin: 20px 0;
        }

        .subscription-status {
            display: flex;
            align-items: center;
            gap: 15px;
            margin-bottom: 20px;
        }

        .status-badge {
            padding: 5px 12px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: 600;
        }

        .status-badge.active {
            background: #10B981;
            color: white;
        }

        .subscription-details {
            background: white;
            padding: 15px;
            border-radius: 6px;
            margin-bottom: 20px;
        }

        .detail-item {
            display: flex;
            justify-content: space-between;
            margin-bottom: 10px;
            padding-bottom: 10px;
            border-bottom: 1px solid #eee;
        }

        .detail-item:last-child {
            margin-bottom: 0;
            padding-bottom: 0;
            border-bottom: none;
        }

        .subscription-benefits ul {
            list-style: none;
            padding: 0;
        }

        .subscription-benefits li {
            margin: 8px 0;
            color: #666;
        }

        .subscription-menu-link {
            display: flex;
            align-items: center;
            gap: 8px;
            text-decoration: none;
        }

        .subscription-icon {
            font-size: 16px;
        }
        </style>

        <script>
        function cancelSubscription() {
            if (confirm('Are you sure you want to cancel your subscription?')) {
                // AJAX call to cancel subscription
                jQuery.ajax({
                    url: '<?php echo admin_url('admin-ajax.php'); ?>',
                    type: 'POST',
                    data: {
                        action: 'cancel_subscription',
                        nonce: '<?php echo wp_create_nonce('cancel_subscription'); ?>'
                    },
                    success: function(response) {
                        if (response.success) {
                            alert('Subscription cancelled successfully.');
                            location.reload();
                        } else {
                            alert('Error cancelling subscription: ' + response.data.message);
                        }
                    }
                });
            }
        }
        </script>
        <?php
    }

    private function get_user_subscription_details($user_id) {
        global $wpdb;
        $subscriptions_table = $wpdb->prefix . 'manga_user_subscriptions';
        $plans_table = $wpdb->prefix . 'manga_subscription_plans';

        $subscription = $wpdb->get_row($wpdb->prepare(
            "SELECT s.*, p.plan_name
             FROM $subscriptions_table s
             LEFT JOIN $plans_table p ON s.plan_id = p.plan_id
             WHERE s.user_id = %d
             AND s.status = 'active'
             AND s.end_date > NOW()
             ORDER BY s.end_date DESC
             LIMIT 1",
            $user_id
        ), ARRAY_A);

        return $subscription;
    }

    public function display_subscription_required_message() {
        // Get current chapter info
        global $wp_manga;
        $wp_manga_functions = madara_get_global_wp_manga_functions();
        $manga_id = get_the_ID();
        $reading_chapter = function_exists('madara_permalink_reading_chapter') ? madara_permalink_reading_chapter() : false;

        if (!$reading_chapter) {
            return; // No chapter data, don't interfere
        }

        // Check if chapter is marked as premium
        $is_premium = get_post_meta($reading_chapter['chapter_id'], '_is_premium_chapter', true);

        if (!$is_premium || $is_premium !== '1') {
            return; // Not a premium chapter, don't interfere
        }

        $user_id = get_current_user_id();

        // Check if user is logged in
        if (!$user_id) {
            echo $this->get_login_required_message();
            return;
        }

        // Check if user has active subscription
        if ($this->user_has_active_subscription($user_id)) {
            return; // User has subscription, don't interfere
        }

        // User doesn't have subscription, show subscription required message
        // Add body class to hide premium content
        echo '<script>document.body.classList.add("premium-chapter-blocked");</script>';
        echo $this->get_subscription_required_message($reading_chapter);
    }

    private function user_has_active_subscription($user_id) {
        global $wpdb;
        $subscriptions_table = $wpdb->prefix . 'manga_user_subscriptions';

        $subscription = $wpdb->get_row($wpdb->prepare(
            "SELECT * FROM $subscriptions_table
             WHERE user_id = %d
             AND status = 'active'
             AND end_date > NOW()
             ORDER BY end_date DESC
             LIMIT 1",
            $user_id
        ));

        return $subscription !== null;
    }

    private function get_login_required_message() {
        ob_start();
        ?>
        <div class="subscription-required-message">
            <div class="message-content">
                <div class="message-icon">🔒</div>
                <h3>Login Required</h3>
                <p>You need to be logged in to access this premium chapter.</p>
                <div class="message-actions">
                    <a href="<?php echo wp_login_url(get_permalink()); ?>" class="button-primary">Login</a>
                    <a href="<?php echo wp_registration_url(); ?>" class="button-secondary">Register</a>
                </div>
            </div>
        </div>
        <?php
        return ob_get_clean();
    }

    private function get_subscription_required_message($chapter) {
        ob_start();
        ?>
        <div class="subscription-required-message">
            <div class="message-content">
                <div class="message-icon">👑</div>
                <h3>Premium Chapter</h3>
                <p>This chapter is available for premium subscribers only.</p>
                <div class="subscription-benefits-mini">
                    <h4>Premium Benefits:</h4>
                    <ul>
                        <li>✅ Unlimited access to all premium chapters</li>
                        <li>✅ Early access to new releases</li>
                        <li>✅ Ad-free reading experience</li>
                        <li>✅ HD quality images</li>
                    </ul>
                </div>
                <div class="message-actions">
                    <button class="button-primary" onclick="openSubscriptionModal()">
                        Subscribe Now
                    </button>
                    <a href="<?php echo home_url('/subscription'); ?>" class="button-secondary">
                        View Plans
                    </a>
                </div>
            </div>
        </div>

        <style>
        .subscription-required-message {
            background: linear-gradient(135deg, #1a1a1a 0%, #2d2d2d 100%);
            border-radius: 16px;
            padding: 40px;
            text-align: center;
            color: white;
            margin: 40px auto;
            border: 2px solid #333;
            max-width: 800px;
            position: relative;
            z-index: 10;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.5);
        }

        /* Child theme compatibility - ensure message appears above content */
        .reading-content .subscription-required-message {
            margin: 20px auto;
            width: 90%;
            max-width: 600px;
        }

        /* User profile page compatibility */
        .user-settings .subscription-required-message,
        .user-profile .subscription-required-message {
            margin: 20px 0;
            padding: 30px;
        }

        /* Mobile responsiveness for child theme */
        @media (max-width: 768px) {
            .reading-content .subscription-required-message {
                margin: 15px auto;
                padding: 20px;
                width: 95%;
            }
        }

        .message-content {
            max-width: 500px;
            margin: 0 auto;
        }

        .message-icon {
            font-size: 4rem;
            margin-bottom: 20px;
        }

        .subscription-required-message h3 {
            font-size: 2rem;
            font-weight: 700;
            margin: 0 0 16px 0;
            color: #FF6B35;
        }

        .subscription-required-message p {
            font-size: 1.1rem;
            color: #ccc;
            margin: 0 0 24px 0;
            line-height: 1.6;
        }

        .subscription-benefits-mini {
            background: rgba(255, 107, 53, 0.1);
            border: 1px solid rgba(255, 107, 53, 0.3);
            border-radius: 12px;
            padding: 20px;
            margin: 24px 0;
            text-align: left;
        }

        .subscription-benefits-mini h4 {
            color: #FF6B35;
            margin: 0 0 12px 0;
            font-size: 1.1rem;
        }

        .subscription-benefits-mini ul {
            list-style: none;
            padding: 0;
            margin: 0;
        }

        .subscription-benefits-mini li {
            color: #ccc;
            margin: 8px 0;
            font-size: 0.95rem;
        }

        .message-actions {
            display: flex;
            gap: 16px;
            justify-content: center;
            margin-top: 24px;
        }

        .button-primary, .button-secondary {
            padding: 14px 28px;
            border-radius: 8px;
            font-weight: 600;
            text-decoration: none;
            cursor: pointer;
            transition: all 0.2s;
            border: none;
            font-size: 1rem;
        }

        .button-primary {
            background: linear-gradient(135deg, #FF6B35 0%, #FF8E53 100%);
            color: white;
            box-shadow: 0 4px 15px rgba(255, 107, 53, 0.3);
        }

        .button-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(255, 107, 53, 0.4);
            color: white;
            text-decoration: none;
        }

        .button-secondary {
            background: rgba(255, 255, 255, 0.1);
            color: #ccc;
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .button-secondary:hover {
            background: rgba(255, 255, 255, 0.2);
            color: white;
            text-decoration: none;
        }

        @media (max-width: 768px) {
            .subscription-required-message {
                padding: 24px;
                margin: 20px 0;
            }

            .message-icon {
                font-size: 3rem;
            }

            .subscription-required-message h3 {
                font-size: 1.5rem;
            }

            .message-actions {
                flex-direction: column;
            }

            .button-primary, .button-secondary {
                width: 100%;
                text-align: center;
            }
        }
        </style>
        <?php
        return ob_get_clean();
    }

    public function ajax_create_payment() {
        // Verify nonce
        if (!wp_verify_nonce($_POST['nonce'], 'subscription_payment')) {
            wp_die(json_encode(array('success' => false, 'data' => array('message' => 'Invalid nonce'))));
        }

        // Check if user is logged in
        if (!is_user_logged_in()) {
            wp_die(json_encode(array('success' => false, 'data' => array('message' => 'User not logged in'))));
        }

        $plan_id = intval($_POST['plan_id']);
        $user_id = get_current_user_id();

        // Get plan details
        global $wpdb;
        $plans_table = $wpdb->prefix . 'manga_subscription_plans';
        $plan = $wpdb->get_row($wpdb->prepare("SELECT * FROM $plans_table WHERE plan_id = %d AND is_active = 1", $plan_id));

        if (!$plan) {
            wp_die(json_encode(array('success' => false, 'data' => array('message' => 'Invalid plan'))));
        }

        // Create PayPal payment
        $payment_data = $this->create_paypal_payment($plan, $user_id);

        if ($payment_data['success']) {
            wp_die(json_encode(array('success' => true, 'data' => $payment_data['data'])));
        } else {
            wp_die(json_encode(array('success' => false, 'data' => $payment_data['data'])));
        }
    }

    public function ajax_cancel_subscription() {
        // Verify nonce
        if (!wp_verify_nonce($_POST['nonce'], 'cancel_subscription')) {
            wp_die(json_encode(array('success' => false, 'data' => array('message' => 'Invalid nonce'))));
        }

        // Check if user is logged in
        if (!is_user_logged_in()) {
            wp_die(json_encode(array('success' => false, 'data' => array('message' => 'User not logged in'))));
        }

        $user_id = get_current_user_id();

        // Cancel user subscription
        global $wpdb;
        $subscriptions_table = $wpdb->prefix . 'manga_user_subscriptions';

        $result = $wpdb->update(
            $subscriptions_table,
            array('status' => 'cancelled'),
            array('user_id' => $user_id, 'status' => 'active'),
            array('%s'),
            array('%d', '%s')
        );

        if ($result !== false) {
            wp_die(json_encode(array('success' => true, 'data' => array('message' => 'Subscription cancelled successfully'))));
        } else {
            wp_die(json_encode(array('success' => false, 'data' => array('message' => 'Failed to cancel subscription'))));
        }
    }

    private function create_paypal_payment($plan, $user_id) {
        // PayPal configuration (you'll need to set these in your WordPress admin)
        $paypal_client_id = get_option('manga_subscription_paypal_client_id', '');
        $paypal_client_secret = get_option('manga_subscription_paypal_client_secret', '');
        $paypal_mode = get_option('manga_subscription_paypal_mode', 'sandbox'); // sandbox or live

        if (empty($paypal_client_id) || empty($paypal_client_secret)) {
            return array(
                'success' => false,
                'data' => array('message' => 'PayPal not configured. Please contact administrator.')
            );
        }

        $base_url = $paypal_mode === 'sandbox' ? 'https://api.sandbox.paypal.com' : 'https://api.paypal.com';

        // Get access token
        $token_response = $this->get_paypal_access_token($base_url, $paypal_client_id, $paypal_client_secret);

        if (!$token_response['success']) {
            return $token_response;
        }

        $access_token = $token_response['data']['access_token'];

        // Create payment
        $payment_data = array(
            'intent' => 'sale',
            'payer' => array(
                'payment_method' => 'paypal'
            ),
            'transactions' => array(
                array(
                    'amount' => array(
                        'total' => number_format($plan->price, 2, '.', ''),
                        'currency' => 'USD'
                    ),
                    'description' => $plan->plan_name . ' Subscription',
                    'custom' => json_encode(array(
                        'user_id' => $user_id,
                        'plan_id' => $plan->plan_id
                    ))
                )
            ),
            'redirect_urls' => array(
                'return_url' => home_url('?paypal_return=success'),
                'cancel_url' => home_url('?paypal_return=cancel')
            )
        );

        $response = wp_remote_post($base_url . '/v1/payments/payment', array(
            'headers' => array(
                'Content-Type' => 'application/json',
                'Authorization' => 'Bearer ' . $access_token
            ),
            'body' => json_encode($payment_data),
            'timeout' => 30
        ));

        if (is_wp_error($response)) {
            return array(
                'success' => false,
                'data' => array('message' => 'PayPal connection error: ' . $response->get_error_message())
            );
        }

        $body = json_decode(wp_remote_retrieve_body($response), true);

        if (isset($body['links'])) {
            foreach ($body['links'] as $link) {
                if ($link['rel'] === 'approval_url') {
                    return array(
                        'success' => true,
                        'data' => array(
                            'approval_url' => $link['href'],
                            'payment_id' => $body['id']
                        )
                    );
                }
            }
        }

        return array(
            'success' => false,
            'data' => array('message' => 'Failed to create PayPal payment')
        );
    }

    private function get_paypal_access_token($base_url, $client_id, $client_secret) {
        $response = wp_remote_post($base_url . '/v1/oauth2/token', array(
            'headers' => array(
                'Accept' => 'application/json',
                'Accept-Language' => 'en_US',
                'Authorization' => 'Basic ' . base64_encode($client_id . ':' . $client_secret)
            ),
            'body' => 'grant_type=client_credentials',
            'timeout' => 30
        ));

        if (is_wp_error($response)) {
            return array(
                'success' => false,
                'data' => array('message' => 'PayPal token error: ' . $response->get_error_message())
            );
        }

        $body = json_decode(wp_remote_retrieve_body($response), true);

        if (isset($body['access_token'])) {
            return array(
                'success' => true,
                'data' => $body
            );
        }

        return array(
            'success' => false,
            'data' => array('message' => 'Failed to get PayPal access token')
        );
    }

    public function add_subscription_modal() {
        if (!is_admin()) {
            include(WP_MANGA_SUBSCRIPTION_DIR . 'templates/modal-subscription.php');
        }
    }

    public function subscription_page_shortcode($atts) {
        ob_start();
        include(WP_MANGA_SUBSCRIPTION_DIR . 'templates/subscription-page.php');
        return ob_get_clean();
    }

    public function add_chapter_premium_setting($chapter_type, $post_id) {
        // This will be called via AJAX, so we need to get the chapter ID from the request
        $chapter_id = isset($_GET['chapter_id']) ? intval($_GET['chapter_id']) : 0;

        if (!$chapter_id) {
            // If no chapter ID, this might be a new chapter, so show the setting anyway
            $is_premium = false;
        } else {
            $is_premium = get_post_meta($chapter_id, '_is_premium_chapter', true);
        }
        ?>
        <div class="wp-manga-modal-subscription">
            <strong><?php esc_html_e('🎯 Subscription Settings:', 'wp-manga-subscription'); ?></strong>
            <br>
            <label style="display: flex; align-items: center; gap: 8px; margin-top: 8px; cursor: pointer;">
                <input type="checkbox" name="is_premium_chapter" id="is_premium_chapter" value="1" <?php checked($is_premium, '1'); ?> />
                <strong>Premium Chapter</strong> - Requires active subscription to access
            </label>
            <span class="desc" style="font-style: italic; color: #666; font-size: 12px; display: block; margin-top: 4px;">
                Check this box to make this chapter available only to premium subscribers.
            </span>
        </div>
        <?php
    }

    public function save_chapter_premium_setting_ajax($manga_id, $chapter_id) {
        // This is called via AJAX when saving chapter
        $is_premium = isset($_POST['is_premium_chapter']) ? '1' : '0';
        update_post_meta($chapter_id, '_is_premium_chapter', $is_premium);
    }

    public function enqueue_admin_scripts() {
        // Only load on manga edit pages
        global $pagenow;
        if ($pagenow === 'post.php' || $pagenow === 'post-new.php') {
            $screen = get_current_screen();
            if ($screen && $screen->post_type === 'wp-manga') {
                wp_add_inline_script('jquery', '
                    jQuery(document).ready(function($) {
                        // Handle premium chapter setting in AJAX save
                        $(document).on("wp_manga_before_admin_save_chapter", function(e, manga_id, chapter_id) {
                            var isPremium = $("#is_premium_chapter").is(":checked") ? "1" : "0";

                            // Add to the AJAX data
                            if (typeof window.mangaChapterSaveData === "undefined") {
                                window.mangaChapterSaveData = {};
                            }
                            window.mangaChapterSaveData.is_premium_chapter = isPremium;

                            // Send separate AJAX request for premium setting
                            $.ajax({
                                url: ajaxurl,
                                type: "POST",
                                data: {
                                    action: "save_chapter_premium_setting",
                                    manga_id: manga_id,
                                    chapter_id: chapter_id,
                                    is_premium_chapter: isPremium,
                                    nonce: "' . wp_create_nonce('save_chapter_premium') . '"
                                }
                            });
                        });
                    });
                ');
            }
        }
    }

    public function ajax_save_chapter_premium_setting() {
        // Verify nonce
        if (!wp_verify_nonce($_POST['nonce'], 'save_chapter_premium')) {
            wp_die('Invalid nonce');
        }

        // Check permissions
        if (!current_user_can('edit_posts')) {
            wp_die('Insufficient permissions');
        }

        $chapter_id = intval($_POST['chapter_id']);
        $is_premium = $_POST['is_premium_chapter'] === '1' ? '1' : '0';

        update_post_meta($chapter_id, '_is_premium_chapter', $is_premium);

        wp_die('success');
    }

    public function add_premium_chapter_class($class, $chapter, $manga_id) {
        $is_premium = get_post_meta($chapter['chapter_id'], '_is_premium_chapter', true);
        if ($is_premium === '1') {
            $class .= ' premium-chapter';
        }
        return $class;
    }

    public function add_premium_badge($chapter, $manga_id) {
        $is_premium = get_post_meta($chapter['chapter_id'], '_is_premium_chapter', true);
        if ($is_premium === '1') {
            // Generate the premium tag structure that matches child theme expectations
            $chapter_url = $this->get_chapter_url($manga_id, $chapter);
            $time_ago = $this->get_chapter_time_ago($chapter);

            echo '<span class="post-on font-meta">';
            echo '<a href="' . esc_url($chapter_url) . '" title="' . esc_attr($time_ago) . '" class="c-premium-tag">';
            echo '<img draggable="false" role="img" class="emoji" alt="👑" src="https://s.w.org/images/core/emoji/16.0.1/svg/1f451.svg"> Premium';
            echo '</a>';
            echo '</span>';
        }
    }

    private function get_chapter_url($manga_id, $chapter) {
        global $wp_manga_functions;
        if (isset($chapter['chapter_slug'])) {
            return $wp_manga_functions->build_chapter_url($manga_id, $chapter['chapter_slug']);
        }
        return get_permalink($manga_id);
    }

    private function get_chapter_time_ago($chapter) {
        if (isset($chapter['chapter_date'])) {
            return human_time_diff(strtotime($chapter['chapter_date']), current_time('timestamp')) . ' ago';
        }
        return 'Premium chapter';
    }

    public function enqueue_frontend_scripts() {
        if (!is_admin()) {
            wp_add_inline_script('jquery', '
                jQuery(document).ready(function($) {
                    // Function to replace "New" tags with proper "Premium" structure
                    function replacePremiumTags() {
                        // Replace new-chapter tags in premium chapters
                        $(".premium-chapter .new-chapter, .premium-chapter .c-new-tag").each(function() {
                            var $this = $(this);
                            if (!$this.hasClass("premium-tag-replaced")) {
                                var $parent = $this.closest(".ch-date");
                                if ($parent.length) {
                                    // Replace the entire ch-date structure with post-on font-meta structure
                                    var chapterUrl = $this.closest("li").find("a").attr("href") || "#";
                                    var timeAgo = $this.attr("title") || "Premium chapter";

                                    var premiumHtml = \'<span class="post-on font-meta">\' +
                                        \'<a href="\' + chapterUrl + \'" title="\' + timeAgo + \'" class="c-premium-tag">\' +
                                        \'<img draggable="false" role="img" class="emoji" alt="👑" src="https://s.w.org/images/core/emoji/16.0.1/svg/1f451.svg"> Premium\' +
                                        \'</a></span>\';

                                    $parent.replaceWith(premiumHtml);
                                } else {
                                    // Fallback: just replace the tag content
                                    $this.removeClass("new-chapter c-new-tag")
                                         .addClass("c-premium-tag premium-tag-replaced")
                                         .html(\'<img draggable="false" role="img" class="emoji" alt="👑" src="https://s.w.org/images/core/emoji/16.0.1/svg/1f451.svg"> Premium\');
                                }
                            }
                        });
                    }

                    // Auto-open subscription modal for premium chapters
                    $(document).on("click", ".premium-chapter a, .c-premium-tag", function(e) {
                        var user_id = ' . (get_current_user_id() ?: 0) . ';
                        var has_subscription = ' . ($this->user_has_active_subscription(get_current_user_id()) ? 'true' : 'false') . ';

                        if (!has_subscription) {
                            e.preventDefault();
                            if (typeof openSubscriptionModal === "function") {
                                openSubscriptionModal();
                            } else {
                                alert("This is a premium chapter. Please subscribe to access it.");
                            }
                            return false;
                        }
                    });

                    // Initial replacement
                    replacePremiumTags();

                    // Re-run after AJAX content loads
                    $(document).on("ajaxComplete", function() {
                        setTimeout(replacePremiumTags, 100);
                    });

                    // Periodic check for dynamically loaded content
                    setInterval(replacePremiumTags, 2000);
                });
            ');

            wp_add_inline_style('wp-manga-style', '
                /* Premium tag styling to match child theme */
                .post-on.font-meta .c-premium-tag {
                    background: linear-gradient(135deg, #FF6B35 0%, #FF8E53 100%) !important;
                    color: white !important;
                    padding: 4px 8px !important;
                    border-radius: 12px !important;
                    font-size: 11px !important;
                    font-weight: 600 !important;
                    box-shadow: 0 2px 8px rgba(255, 107, 53, 0.3) !important;
                    text-decoration: none !important;
                    display: inline-flex !important;
                    align-items: center !important;
                    gap: 4px !important;
                }

                .post-on.font-meta .c-premium-tag:hover {
                    background: linear-gradient(135deg, #FF8E53 0%, #FF6B35 100%) !important;
                    transform: translateY(-1px) !important;
                    box-shadow: 0 4px 12px rgba(255, 107, 53, 0.4) !important;
                }

                .post-on.font-meta .c-premium-tag .emoji {
                    width: 12px !important;
                    height: 12px !important;
                    margin: 0 !important;
                }

                /* Fallback premium tag styling */
                .c-premium-tag {
                    background: linear-gradient(135deg, #FF6B35 0%, #FF8E53 100%) !important;
                    color: white !important;
                    padding: 4px 8px !important;
                    border-radius: 12px !important;
                    font-size: 11px !important;
                    font-weight: 600 !important;
                    box-shadow: 0 2px 8px rgba(255, 107, 53, 0.3) !important;
                    text-decoration: none !important;
                    display: inline-flex !important;
                    align-items: center !important;
                    gap: 4px !important;
                }

                .premium-badge {
                    background: linear-gradient(135deg, #FF6B35 0%, #FF8E53 100%);
                    color: white;
                    padding: 2px 8px;
                    border-radius: 12px;
                    font-size: 11px;
                    font-weight: 600;
                    margin-left: 8px;
                    display: inline-block;
                    box-shadow: 0 2px 8px rgba(255, 107, 53, 0.3);
                }

                .premium-chapter {
                    position: relative;
                }

                .premium-chapter::before {
                    content: "";
                    position: absolute;
                    top: 0;
                    left: 0;
                    right: 0;
                    bottom: 0;
                    background: linear-gradient(45deg, transparent 30%, rgba(255, 107, 53, 0.1) 50%, transparent 70%);
                    pointer-events: none;
                    border-radius: 4px;
                }

                .premium-chapter a {
                    color: #FF6B35 !important;
                    font-weight: 600;
                }

                .premium-chapter:hover {
                    background: rgba(255, 107, 53, 0.05);
                    border-radius: 4px;
                }

                /* Hide premium content for non-subscribers */
                body.premium-chapter-blocked .page-break {
                    display: none !important;
                }

                body.premium-chapter-blocked .wp-manga-chapter-img {
                    display: none !important;
                }

                body.premium-chapter-blocked .reading-content .page-break {
                    display: none !important;
                }
            ');
        }
    }

    public function filter_premium_chapter_images($images_data) {
        // Get current chapter info
        global $wp_manga;
        $wp_manga_functions = madara_get_global_wp_manga_functions();
        $manga_id = get_the_ID();
        $reading_chapter = function_exists("madara_permalink_reading_chapter") ? madara_permalink_reading_chapter() : false;

        if (!$reading_chapter) {
            return $images_data;
        }

        // Check if chapter is premium
        $is_premium = get_post_meta($reading_chapter["chapter_id"], "_is_premium_chapter", true);

        if ($is_premium !== "1") {
            return $images_data; // Not premium, return original images
        }

        $user_id = get_current_user_id();

        // If user has subscription, return original images
        if ($user_id && $this->user_has_active_subscription($user_id)) {
            return $images_data;
        }

        // User doesnt have subscription, return empty array to block images
        return array();
    }

    public function filter_chapter_content($content, $chapter_id) {
        // Check if chapter is premium
        $is_premium = get_post_meta($chapter_id, '_is_premium_chapter', true);

        if ($is_premium !== '1') {
            return $content; // Not premium, return original content
        }

        $user_id = get_current_user_id();

        // If user has subscription, return original content
        if ($user_id && $this->user_has_active_subscription($user_id)) {
            return $content;
        }

        // User doesn't have subscription, return subscription message only
        return $this->get_subscription_required_message(array('chapter_id' => $chapter_id));
    }

    private function handle_bulk_premium_setting() {
        if (!current_user_can('manage_options')) {
            return;
        }

        $manga_id = intval($_POST['manga_id']);
        $action = sanitize_text_field($_POST['premium_action']);
        $chapter_range = sanitize_text_field($_POST['chapter_range']);

        if (!$manga_id) {
            echo '<div class="notice notice-error"><p>Please select a manga.</p></div>';
            return;
        }

        // Get chapters for the manga
        global $wp_manga_chapter;
        $chapters = $wp_manga_chapter->get_chapters($manga_id);

        if (empty($chapters)) {
            echo '<div class="notice notice-error"><p>No chapters found for this manga.</p></div>';
            return;
        }

        $updated_count = 0;
        $premium_value = ($action === 'set_premium') ? '1' : '0';

        if ($chapter_range === 'all') {
            // Set all chapters
            foreach ($chapters as $chapter) {
                update_post_meta($chapter['chapter_id'], '_is_premium_chapter', $premium_value);
                $updated_count++;
            }
        } else {
            // Parse range like "1-10" or "5,7,9"
            if (strpos($chapter_range, '-') !== false) {
                // Range format: "1-10"
                list($start, $end) = explode('-', $chapter_range);
                $start = intval($start);
                $end = intval($end);

                foreach ($chapters as $chapter) {
                    $chapter_number = intval($chapter['chapter_name']);
                    if ($chapter_number >= $start && $chapter_number <= $end) {
                        update_post_meta($chapter['chapter_id'], '_is_premium_chapter', $premium_value);
                        $updated_count++;
                    }
                }
            } else {
                // Comma-separated format: "5,7,9"
                $chapter_numbers = array_map('intval', explode(',', $chapter_range));

                foreach ($chapters as $chapter) {
                    $chapter_number = intval($chapter['chapter_name']);
                    if (in_array($chapter_number, $chapter_numbers)) {
                        update_post_meta($chapter['chapter_id'], '_is_premium_chapter', $premium_value);
                        $updated_count++;
                    }
                }
            }
        }

        $action_text = ($action === 'set_premium') ? 'set as premium' : 'set as free';
        echo '<div class="notice notice-success"><p>Successfully ' . $action_text . ' for ' . $updated_count . ' chapters.</p></div>';
    }

    private function display_chapter_management() {
        // Get all manga
        $manga_posts = get_posts(array(
            'post_type' => 'wp-manga',
            'posts_per_page' => -1,
            'post_status' => 'publish'
        ));

        ?>
        <div class="card">
            <h2>📚 Chapter Premium Management</h2>

            <div style="background: #f0f8ff; border: 1px solid #0073aa; border-radius: 8px; padding: 16px; margin-bottom: 20px;">
                <h3 style="margin-top: 0; color: #0073aa;">How to Set Chapters as Premium:</h3>
                <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px;">
                    <div>
                        <h4>Method 1: Individual Chapters</h4>
                        <ol>
                            <li>Go to <strong>WP Manga → All Manga</strong></li>
                            <li>Click on any manga</li>
                            <li>Click <strong>"Chapters"</strong> tab</li>
                            <li>Click <strong>"Edit"</strong> on any chapter</li>
                            <li>Look for <strong>"🎯 Subscription Settings"</strong></li>
                            <li>Check <strong>"Premium Chapter"</strong> box</li>
                            <li>Save the chapter</li>
                        </ol>
                    </div>
                    <div>
                        <h4>Method 2: Bulk Setting (Below)</h4>
                        <ol>
                            <li>Select a manga from dropdown</li>
                            <li>Choose action (Set Premium/Set Free)</li>
                            <li>Specify chapters:
                                <ul>
                                    <li><code>all</code> - All chapters</li>
                                    <li><code>1-10</code> - Chapters 1 to 10</li>
                                    <li><code>5,7,9</code> - Specific chapters</li>
                                </ul>
                            </li>
                            <li>Click "Apply"</li>
                        </ol>
                    </div>
                </div>
            </div>

            <form method="post" action="">
                <table class="form-table">
                    <tr>
                        <th scope="row">Select Manga</th>
                        <td>
                            <select name="manga_id" required>
                                <option value="">Choose a manga...</option>
                                <?php foreach ($manga_posts as $manga): ?>
                                    <option value="<?php echo $manga->ID; ?>"><?php echo esc_html($manga->post_title); ?></option>
                                <?php endforeach; ?>
                            </select>
                        </td>
                    </tr>
                    <tr>
                        <th scope="row">Action</th>
                        <td>
                            <select name="premium_action" required>
                                <option value="set_premium">Set as Premium (Requires Subscription)</option>
                                <option value="set_free">Set as Free (No Subscription Required)</option>
                            </select>
                        </td>
                    </tr>
                    <tr>
                        <th scope="row">Chapter Range</th>
                        <td>
                            <input type="text" name="chapter_range" value="all" placeholder="all, 1-10, or 5,7,9" required />
                            <p class="description">
                                <strong>Examples:</strong><br>
                                • <code>all</code> - All chapters<br>
                                • <code>1-10</code> - Chapters 1 through 10<br>
                                • <code>5,7,9</code> - Specific chapters 5, 7, and 9
                            </p>
                        </td>
                    </tr>
                </table>
                <?php submit_button('Apply Premium Settings', 'primary', 'bulk_set_premium'); ?>
            </form>
        </div>
        <?php
    }

    public function subscribe_button_shortcode($atts) {
        $atts = shortcode_atts(array(
            'text' => 'Subscribe Now',
            'style' => 'primary', // primary, secondary, minimal
            'size' => 'medium' // small, medium, large
        ), $atts);

        $user_id = get_current_user_id();
        $has_subscription = $user_id ? $this->user_has_active_subscription($user_id) : false;

        if ($has_subscription) {
            return '<span class="subscription-status-badge">✅ Premium Member</span>';
        }

        $button_class = 'manga-subscribe-btn manga-subscribe-btn-' . $atts['style'] . ' manga-subscribe-btn-' . $atts['size'];

        ob_start();
        ?>
        <button class="<?php echo esc_attr($button_class); ?>" onclick="openSubscriptionModal()">
            <span><?php echo esc_html($atts['text']); ?></span>
        </button>

        <style>
        .manga-subscribe-btn {
            border: none;
            border-radius: 8px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.2s;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            text-decoration: none;
        }

        .manga-subscribe-btn-primary {
            background: linear-gradient(135deg, #FF6B35 0%, #FF8E53 100%);
            color: white;
            box-shadow: 0 2px 10px rgba(255, 107, 53, 0.3);
        }

        .manga-subscribe-btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(255, 107, 53, 0.4);
        }

        .manga-subscribe-btn-secondary {
            background: #f5f5f5;
            color: #333;
            border: 1px solid #ddd;
        }

        .manga-subscribe-btn-secondary:hover {
            background: #e5e5e5;
        }

        .manga-subscribe-btn-minimal {
            background: transparent;
            color: #FF6B35;
            border: 2px solid #FF6B35;
        }

        .manga-subscribe-btn-minimal:hover {
            background: #FF6B35;
            color: white;
        }

        .manga-subscribe-btn-small {
            padding: 8px 16px;
            font-size: 0.9rem;
        }

        .manga-subscribe-btn-medium {
            padding: 12px 24px;
            font-size: 1rem;
        }

        .manga-subscribe-btn-large {
            padding: 16px 32px;
            font-size: 1.1rem;
        }

        .subscription-status-badge {
            background: #10B981;
            color: white;
            padding: 8px 16px;
            border-radius: 20px;
            font-size: 0.9rem;
            font-weight: 600;
            display: inline-block;
        }
        </style>
        <?php
        return ob_get_clean();
    }

    public function enqueue_child_theme_compatibility() {
        if (!is_admin()) {
            // Provide data for child theme's premium-chapters.js
            wp_localize_script('jquery', 'mangaSubscriptionData', array(
                'ajaxurl' => admin_url('admin-ajax.php'),
                'nonce' => wp_create_nonce('manga_subscription_check'),
                'isUserLoggedIn' => is_user_logged_in(),
                'hasActiveSubscription' => $this->user_has_active_subscription(get_current_user_id()),
                'subscriptionPageUrl' => home_url('/subscription/'),
                'openModalFunction' => 'openSubscriptionModal'
            ));

            // Add compatibility script to work with child theme
            wp_add_inline_script('jquery', '
                // Extend child theme premium chapters functionality
                if (typeof window.premiumChapters === "undefined") {
                    window.premiumChapters = mangaSubscriptionData;
                }

                // Global function for child theme compatibility
                window.openSubscriptionModal = function() {
                    if (jQuery("#subscription-modal").length) {
                        jQuery("#subscription-modal").show();
                        jQuery("body").css("overflow", "hidden");
                    } else {
                        window.location.href = mangaSubscriptionData.subscriptionPageUrl;
                    }
                };

                // Enhanced premium chapter detection and handling
                jQuery(document).ready(function($) {
                    // Function to mark premium chapters based on plugin data
                    function markPremiumChapters() {
                        $("a").each(function() {
                            var href = $(this).attr("href");
                            if (href && href.indexOf("/chapter-") !== -1) {
                                var $container = $(this).closest("li, .unit, .item");
                                var chapterId = $(this).data("chapter-id");

                                // Check if this chapter is marked as premium in the backend
                                if ($container.find(".c-premium-tag").length > 0 ||
                                    $container.hasClass("premium-chapter")) {
                                    $container.addClass("premium-chapter premium-locked");

                                    // If user doesn\'t have subscription, modify the link
                                    if (!mangaSubscriptionData.hasActiveSubscription) {
                                        $(this).attr("href", "#premium-chapter-" + (chapterId || "locked"));
                                    }
                                }
                            }
                        });
                    }

                    // Initial marking
                    markPremiumChapters();

                    // Re-mark after AJAX loads
                    $(document).on("ajaxComplete", function() {
                        setTimeout(markPremiumChapters, 100);
                    });
                });
            ', 'after');
        }
    }
}

// Initialize the isolated plugin
try {
    $wp_manga_subscription_isolated = WP_MANGA_SUBSCRIPTION_ISOLATED::get_instance();
    
    // Show activation notice only once
    if (get_transient('wp_manga_subscription_isolated_activated')) {
        add_action('admin_notices', function() {
            if (function_exists('current_user_can') && current_user_can('manage_options')) {
                echo '<div class="notice notice-success is-dismissible">';
                echo '<p><strong>✅ WP Manga Subscription:</strong> Plugin activated! No interference with manga editing. Check WP Manga → Subscriptions for setup.</p>';
                echo '</div>';
            }
        });
        delete_transient('wp_manga_subscription_isolated_activated');
    }
    
} catch (Exception $e) {
    error_log('WP Manga Subscription Isolated: Plugin initialization failed - ' . $e->getMessage());
}

// Include test functionality in development/testing environments
if (defined('WP_DEBUG') && WP_DEBUG) {
    require_once plugin_dir_path(__FILE__) . 'test-plugin-functionality.php';
}

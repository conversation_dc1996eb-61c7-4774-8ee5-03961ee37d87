<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Premium Tags Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        
        .test-section {
            background: white;
            padding: 20px;
            margin: 20px 0;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .chapter-list {
            list-style: none;
            padding: 0;
        }
        
        .chapter-list li {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 10px;
            margin: 5px 0;
            background: #f8f9fa;
            border-radius: 4px;
        }
        
        .chapter-title {
            font-weight: 600;
        }
        
        .ch-date {
            display: flex;
            align-items: center;
            gap: 10px;
        }
        
        .new-chapter {
            background: #28a745;
            color: white;
            padding: 2px 8px;
            border-radius: 12px;
            font-size: 11px;
            font-weight: 600;
        }
        
        /* This will be replaced by the plugin */
        .post-on.font-meta .c-premium-tag {
            background: linear-gradient(135deg, #FF6B35 0%, #FF8E53 100%);
            color: white;
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 11px;
            font-weight: 600;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 4px;
        }
        
        .premium-chapter {
            background: rgba(255, 107, 53, 0.05);
        }
        
        .test-button {
            background: #007cba;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        
        .test-button:hover {
            background: #005a87;
        }
        
        .result {
            margin: 10px 0;
            padding: 10px;
            border-radius: 4px;
        }
        
        .result.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        
        .result.error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
    </style>
</head>
<body>
    <h1>🧪 Premium Tags Test Page</h1>
    
    <div class="test-section">
        <h2>Test Chapter List (Before Plugin Processing)</h2>
        <p>This simulates how chapters appear before the plugin processes them:</p>
        
        <ul class="chapter-list">
            <li class="premium-chapter">
                <span class="chapter-title">Chapter 1: The Beginning</span>
                <span class="ch-date">
                    <span class="new-chapter"><i class="fa-solid fa-fire"></i> New</span>
                </span>
            </li>
            
            <li class="premium-chapter">
                <span class="chapter-title">Chapter 2: The Journey</span>
                <span class="ch-date">
                    <span class="new-chapter">New</span>
                </span>
            </li>
            
            <li>
                <span class="chapter-title">Chapter 3: Free Chapter</span>
                <span class="ch-date">
                    <span class="new-chapter">New</span>
                </span>
            </li>
        </ul>
    </div>
    
    <div class="test-section">
        <h2>Expected Result (After Plugin Processing)</h2>
        <p>This is what the plugin should generate:</p>
        
        <ul class="chapter-list">
            <li class="premium-chapter">
                <span class="chapter-title">Chapter 1: The Beginning</span>
                <span class="post-on font-meta">
                    <a href="#premium-chapter-1" title="Premium chapter" class="c-premium-tag">
                        <img draggable="false" role="img" class="emoji" alt="👑" src="https://s.w.org/images/core/emoji/16.0.1/svg/1f451.svg"> Premium
                    </a>
                </span>
            </li>
            
            <li class="premium-chapter">
                <span class="chapter-title">Chapter 2: The Journey</span>
                <span class="post-on font-meta">
                    <a href="#premium-chapter-2" title="Premium chapter" class="c-premium-tag">
                        <img draggable="false" role="img" class="emoji" alt="👑" src="https://s.w.org/images/core/emoji/16.0.1/svg/1f451.svg"> Premium
                    </a>
                </span>
            </li>
            
            <li>
                <span class="chapter-title">Chapter 3: Free Chapter</span>
                <span class="ch-date">
                    <span class="new-chapter">New</span>
                </span>
            </li>
        </ul>
    </div>
    
    <div class="test-section">
        <h2>Plugin Functionality Tests</h2>
        
        <button class="test-button" onclick="testJavaScriptLoaded()">Test: JavaScript Loaded</button>
        <button class="test-button" onclick="testPremiumTagReplacement()">Test: Premium Tag Replacement</button>
        <button class="test-button" onclick="testModalFunctionality()">Test: Modal Functionality</button>
        <button class="test-button" onclick="testAjaxEndpoint()">Test: AJAX Endpoint</button>
        
        <div id="test-results"></div>
    </div>
    
    <div class="test-section">
        <h2>Manual Tests</h2>
        <p>Click on the premium chapter links above to test the modal functionality.</p>
        <p>If the plugin is working correctly:</p>
        <ul>
            <li>✅ "New" tags in premium chapters should be replaced with crown "Premium" tags</li>
            <li>✅ Clicking premium chapter links should show a subscription modal</li>
            <li>✅ The modal should have proper styling and functionality</li>
            <li>✅ Non-premium chapters should remain unchanged</li>
        </ul>
    </div>

    <!-- Include jQuery for testing -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    
    <!-- Simulate WordPress environment -->
    <script>
        // Simulate mangaSubscriptionData that would be provided by WordPress
        window.mangaSubscriptionData = {
            ajaxurl: '/wp-admin/admin-ajax.php',
            nonce: 'test-nonce-123',
            isUserLoggedIn: false,
            hasActiveSubscription: false,
            subscriptionPageUrl: '/subscription/',
            pluginUrl: './'
        };
        
        // Test functions
        function testJavaScriptLoaded() {
            const results = document.getElementById('test-results');
            let html = '<div class="result ';
            
            if (typeof jQuery !== 'undefined') {
                html += 'success">✅ jQuery is loaded</div>';
            } else {
                html += 'error">❌ jQuery is not loaded</div>';
            }
            
            if (typeof openSubscriptionModal === 'function') {
                html += '<div class="result success">✅ openSubscriptionModal function exists</div>';
            } else {
                html += '<div class="result error">❌ openSubscriptionModal function not found</div>';
            }
            
            if (typeof mangaSubscriptionData !== 'undefined') {
                html += '<div class="result success">✅ mangaSubscriptionData is available</div>';
            } else {
                html += '<div class="result error">❌ mangaSubscriptionData not found</div>';
            }
            
            results.innerHTML = html;
        }
        
        function testPremiumTagReplacement() {
            const results = document.getElementById('test-results');
            const premiumTags = document.querySelectorAll('.c-premium-tag');
            const newTags = document.querySelectorAll('.premium-chapter .new-chapter');
            
            let html = '<div class="result ';
            
            if (premiumTags.length > 0) {
                html += 'success">✅ Found ' + premiumTags.length + ' premium tags</div>';
            } else {
                html += 'error">❌ No premium tags found</div>';
            }
            
            if (newTags.length === 0) {
                html += '<div class="result success">✅ All "New" tags in premium chapters have been replaced</div>';
            } else {
                html += '<div class="result error">❌ ' + newTags.length + ' "New" tags still exist in premium chapters</div>';
            }
            
            results.innerHTML = html;
        }
        
        function testModalFunctionality() {
            const results = document.getElementById('test-results');
            
            if (typeof openSubscriptionModal === 'function') {
                try {
                    openSubscriptionModal();
                    results.innerHTML = '<div class="result success">✅ Modal function executed (check if modal appeared)</div>';
                } catch (e) {
                    results.innerHTML = '<div class="result error">❌ Error executing modal function: ' + e.message + '</div>';
                }
            } else {
                results.innerHTML = '<div class="result error">❌ openSubscriptionModal function not available</div>';
            }
        }
        
        function testAjaxEndpoint() {
            const results = document.getElementById('test-results');
            
            if (typeof jQuery !== 'undefined' && typeof mangaSubscriptionData !== 'undefined') {
                jQuery.ajax({
                    url: mangaSubscriptionData.ajaxurl,
                    type: 'POST',
                    data: {
                        action: 'check_premium_access',
                        chapter_id: 1,
                        nonce: mangaSubscriptionData.nonce
                    },
                    success: function(response) {
                        results.innerHTML = '<div class="result success">✅ AJAX endpoint responded: ' + JSON.stringify(response) + '</div>';
                    },
                    error: function(xhr, status, error) {
                        results.innerHTML = '<div class="result error">❌ AJAX error: ' + error + ' (This is expected in this test environment)</div>';
                    }
                });
            } else {
                results.innerHTML = '<div class="result error">❌ jQuery or mangaSubscriptionData not available for AJAX test</div>';
            }
        }
        
        // Auto-run basic test on page load
        jQuery(document).ready(function() {
            setTimeout(testJavaScriptLoaded, 1000);
        });
    </script>
</body>
</html>

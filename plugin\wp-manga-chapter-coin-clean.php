<?php
/**
 *  Plugin Name: WP Manga - Chapter Subscription (Clean)
 *  Description: Subscription-based access system for premium manga chapters - Clean version without license system
 *  Plugin URI: https://www.mangabooth.com/
 *  Author: Man<PERSON>Booth
 *  Author URI: https://themeforest.net/user/wpstylish
 *  Author Email: <EMAIL>
 *  Version: 2.0.0-clean
 *  Text Domain: wp-manga-chapter-coin
 * @since 1.0
 *
 * @required - WP Manga Core *******
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

// Define constants
if (!defined('WP_MANGA_CHAPTER_COIN_FILE')) {
    define('WP_MANGA_CHAPTER_COIN_FILE', __FILE__);
}

if (!defined('WP_MANGA_CHAPTER_COIN_URI')) {
    define('WP_MANGA_CHAPTER_COIN_URI', plugin_dir_url(__FILE__));
}

if (!defined('WP_MANGA_CHAPTER_COIN_DIR')) {
    define('WP_MANGA_CHAPTER_COIN_DIR', plugin_dir_path(__FILE__));
}

define('MANGA_CHAPTER_COIN_TEXT_DOMAIN', 'wp-manga-chapter-coin');

// Development mode - no license system
define('WP_MANGA_SUBSCRIPTION_DEV_MODE', true);

// Check if WP Manga Core is available before proceeding
if (!class_exists('WP_MANGA')) {
    add_action('admin_notices', function() {
        echo '<div class="notice notice-error is-dismissible">';
        echo '<p><strong>WP Manga - Chapter Subscription:</strong> This plugin requires WP Manga Core to be installed and activated.</p>';
        echo '</div>';
    });
    return;
}

// Include required files with error handling
$required_files = array(
    'admin/dbsetup.php',
    'admin/subscription-manager.php',
    'admin/backend.php',
    // 'admin/reporter.php', // Disabled - requires myCRED
    'inc/shortcodes.php',
    'inc/helper.php',
    'inc/subscription-helper.php'
);

foreach ($required_files as $file) {
    $file_path = WP_MANGA_CHAPTER_COIN_DIR . $file;
    if (file_exists($file_path)) {
        require_once($file_path);
    } else {
        error_log("WP Manga Subscription: Required file missing: {$file}");
    }
}

/**
 * Main plugin class
 */
class WP_MANGA_ADDON_CHAPTER_COIN {
    private static $instance;

    public static function get_instance() {
        if (null == self::$instance) {
            self::$instance = new WP_MANGA_ADDON_CHAPTER_COIN();
        }
        return self::$instance;
    }

    private function __construct() {
        add_action('init', array($this, 'init'));
        
        // Plugin activation hook
        register_activation_hook(__FILE__, array($this, 'activate'));
    }

    public function init() {
        try {
            // Initialize database if needed
            $this->init_database();
            
            // Initialize admin functions
            $this->init_admin_functions();
            
            // Add hooks
            $this->add_hooks();
            
        } catch (Exception $e) {
            error_log('WP Manga Subscription Init Error: ' . $e->getMessage());
        }
    }

    public function activate() {
        try {
            // Setup database on activation
            if (function_exists('wmcc_setup_db')) {
                wmcc_setup_db();
            }

            // Set activation flag and notice
            update_option('wp_manga_subscription_activated', time());
            set_transient('wp_manga_subscription_activation_notice', true, 60);

        } catch (Exception $e) {
            error_log('WP Manga Subscription Activation Error: ' . $e->getMessage());
        }
    }

    private function init_database() {
        if (get_option('wp_manga_chapter_coin_db_ver', '') == '') {
            try {
                if (function_exists('wmcc_setup_db')) {
                    wmcc_setup_db();
                }
            } catch (Exception $e) {
                error_log('WP Manga Subscription: Database setup failed - ' . $e->getMessage());
            }
        }
    }

    private function init_admin_functions() {
        try {
            if (class_exists('WP_MANGA_ADDON_CHAPTER_COIN_BACKEND')) {
                WP_MANGA_ADDON_CHAPTER_COIN_BACKEND::get_instance();
            }

            // Only initialize reporter if myCRED is available
            if (class_exists('WP_MANGA_ADDON_CHAPTER_COIN_REPORT') && class_exists('myCRED_Query_Log')) {
                WP_MANGA_ADDON_CHAPTER_COIN_REPORT::get_instance();
            }
        } catch (Exception $e) {
            error_log('WP Manga Subscription: Admin functions initialization failed - ' . $e->getMessage());
        }
    }

    private function add_hooks() {
        // Frontend hooks
        add_action('wp_enqueue_scripts', array($this, 'enqueue_scripts'));
        add_action('wp_footer', array($this, 'wp_footer'));
        
        // User menu hooks
        add_filter('wp_manga_user_menu_before_items', array($this, 'wp_manga_user_menu_before_items'));
        add_action('wp_manga_user_settings_tab_nav', array($this, 'user_settings_tab_nav'), 10, 2);
        add_action('wp_manga_user_settings_tab_content', array($this, 'user_settings_tab_content'), 10, 2);
        
        // Chapter hooks
        add_filter('wp_manga_chapter_content_alternative', array($this, 'wp_manga_chapter_content_alternative'), 10, 3);
        add_action('wp_manga_chapter_before_name', array($this, 'add_before_chapter_name'), 10, 2);
        
        // Admin hooks
        add_filter('body_class', array($this, 'body_custom_class'));
        
        // Subscription cleanup
        add_action('wp_login', array($this, 'check_subscription_on_login'), 10, 2);
    }

    public function enqueue_scripts() {
        wp_enqueue_script('wp-manga-chapter-coin-js', WP_MANGA_CHAPTER_COIN_URI . 'assets/js/frontend.js', array('jquery'), '2.0.0', true);
        wp_enqueue_style('wp-manga-chapter-coin-css', WP_MANGA_CHAPTER_COIN_URI . 'assets/css/chapter-coin.css', array(), '2.0.0');
    }

    public function wp_footer() {
        $this->load_template('modal', 'subscribe');
        
        $user_id = get_current_user_id();
        if ($user_id) {
            $subscription_status = $this->get_user_subscription_status($user_id);
            echo '<input type="hidden" value="' . ($subscription_status['has_subscription'] ? '1' : '0') . '" id="wp_manga_chapter_subscription_status"/>';
        }
    }

    public function get_user_subscription_status($user_id) {
        try {
            if (!class_exists('WP_MANGA_SUBSCRIPTION_MANAGER')) {
                return array(
                    'has_subscription' => false,
                    'plan_name' => '',
                    'end_date' => '',
                    'status' => ''
                );
            }
            
            $subscription_manager = WP_MANGA_SUBSCRIPTION_MANAGER::get_instance();
            $subscription = $subscription_manager->get_user_subscription($user_id);
            
            if ($subscription) {
                return array(
                    'has_subscription' => true,
                    'plan_name' => $subscription->plan_name,
                    'end_date' => $subscription->end_date,
                    'status' => $subscription->status
                );
            }
        } catch (Exception $e) {
            error_log('WP Manga Subscription: Get user status failed - ' . $e->getMessage());
        }
        
        return array(
            'has_subscription' => false,
            'plan_name' => '',
            'end_date' => '',
            'status' => ''
        );
    }

    public function wp_manga_user_menu_before_items($html) {
        $subscription_status = $this->get_user_subscription_status(get_current_user_id());
        
        global $wp_manga_user_actions;
        if (isset($wp_manga_user_actions)) {
            $url = $wp_manga_user_actions->get_user_tab_url('subscription');
            
            if ($subscription_status['has_subscription']) {
                $html .= '<li><a href="' . esc_url($url) . '">' . sprintf(wp_kses_post(__('Subscription: <i class="fas fa-crown"></i> %s', MANGA_CHAPTER_COIN_TEXT_DOMAIN)), esc_html($subscription_status['plan_name'])) . '</a></li>';
            } else {
                $html .= '<li><a href="' . esc_url($url) . '">' . wp_kses_post(__('Subscription: <i class="fas fa-crown"></i> None', MANGA_CHAPTER_COIN_TEXT_DOMAIN)) . '</a></li>';
            }
        }
        
        return $html;
    }

    public function user_settings_tab_nav($tab_pane, $account) {
        global $wp_manga_user_actions;
        if (isset($wp_manga_user_actions)) {
            ?>
            <li class="<?php echo esc_attr($tab_pane == 'subscription' ? 'active' : ''); ?>">
                <a href="<?php echo esc_url($wp_manga_user_actions->get_user_tab_url('subscription')); ?>">
                    <i class="fas fa-crown"></i><?php echo esc_html__('My Subscription', MANGA_CHAPTER_COIN_TEXT_DOMAIN); ?>
                </a>
            </li>
            <?php
        }
    }

    public function user_settings_tab_content($tab_pane, $account) {
        if ($tab_pane == 'subscription') {
            $this->load_template('user-settings', 'subscription');
        }
    }

    public function load_template($template, $name) {
        $template_file = WP_MANGA_CHAPTER_COIN_DIR . "templates/{$template}-{$name}.php";
        if (file_exists($template_file)) {
            include($template_file);
        }
    }

    public function check_subscription_on_login($user_login, $user) {
        try {
            if (class_exists('WP_MANGA_SUBSCRIPTION_MANAGER')) {
                $subscription_manager = WP_MANGA_SUBSCRIPTION_MANAGER::get_instance();
                $subscription_manager->update_expired_subscriptions();
                
                // Clear user subscription cache
                wp_cache_delete($user->ID . '_subscription', 'manga_subscriptions');
            }
        } catch (Exception $e) {
            error_log('WP Manga Subscription: Login check failed - ' . $e->getMessage());
        }
    }

    public function body_custom_class($classes) {
        $classes[] = 'wp-manga-subscription-enabled';
        return $classes;
    }

    // Placeholder methods for compatibility
    public function wp_manga_chapter_content_alternative($content, $chapter, $user_id) {
        return $content;
    }

    public function add_before_chapter_name($chapter, $manga_id) {
        // Placeholder for chapter badge display
    }
}

// Initialize the plugin - NO LICENSE CHECKS
try {
    $wp_manga_chapter_coin = WP_MANGA_ADDON_CHAPTER_COIN::get_instance();
    
    // Show development notice only once after activation
    if (get_transient('wp_manga_subscription_activation_notice')) {
        add_action('admin_notices', function() {
            if (function_exists('current_user_can') && current_user_can('manage_options')) {
                echo '<div class="notice notice-success is-dismissible">';
                echo '<p><strong>✅ WP Manga Subscription:</strong> Plugin activated successfully in development mode (no license required).</p>';
                echo '</div>';
            }
        });
        delete_transient('wp_manga_subscription_activation_notice');
    }
    
} catch (Exception $e) {
    error_log('WP Manga Subscription: Plugin initialization failed - ' . $e->getMessage());
    add_action('admin_notices', function() use ($e) {
        echo '<div class="notice notice-error is-dismissible">';
        echo '<p><strong>WP Manga - Chapter Subscription:</strong> Plugin initialization failed. Check error logs for details.</p>';
        echo '</div>';
    });
}

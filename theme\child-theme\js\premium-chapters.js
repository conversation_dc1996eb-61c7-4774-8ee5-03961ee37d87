/**
 * Premium Chapter Functionality for Child Theme
 * Handles premium chapter access, modal display, and user redirection
 */

jQuery(document).ready(function($) {
    
    /**
     * Handle premium chapter link clicks
     */
    function handlePremiumChapterClick(e) {
        const $link = $(this);
        const href = $link.attr('href');
        
        // Check if this is a premium chapter link
        if (href && href.startsWith('#premium-chapter-')) {
            e.preventDefault();
            e.stopPropagation();
            
            const chapterId = href.replace('#premium-chapter-', '');
            
            // If user is not logged in, redirect to subscription page
            if (!premiumChapters.isUserLoggedIn) {
                window.location.href = premiumChapters.subscriptionPageUrl;
                return false;
            }
            
            // If user is logged in, check access and show modal if needed
            checkPremiumAccess(chapterId);
            return false;
        }
    }
    
    /**
     * Check premium chapter access via AJAX
     */
    function checkPremiumAccess(chapterId) {
        $.ajax({
            url: premiumChapters.ajaxurl,
            type: 'POST',
            data: {
                action: 'check_premium_access',
                chapter_id: chapterId,
                nonce: premiumChapters.nonce
            },
            success: function(response) {
                if (response.success) {
                    // User has access, reload page to show chapter
                    window.location.reload();
                } else {
                    // Show subscription modal
                    if (typeof openSubscriptionModal === 'function') {
                        openSubscriptionModal();
                    } else if ($('#frm-wp-manga-subscribe').length) {
                        $('#frm-wp-manga-subscribe').modal('show');
                    } else {
                        // Fallback: redirect to subscription page
                        window.location.href = premiumChapters.subscriptionPageUrl;
                    }
                }
            },
            error: function() {
                // On error, redirect to subscription page
                window.location.href = premiumChapters.subscriptionPageUrl;
            }
        });
    }
    
    /**
     * Initialize premium chapter functionality
     */
    function initPremiumChapters() {
        // Bind click handlers to premium chapter links
        $(document).on('click', 'a[href^="#premium-chapter-"]', handlePremiumChapterClick);
        
        // Handle premium chapter items in lists
        $(document).on('click', '.premium-chapter.premium-locked a', handlePremiumChapterClick);
        
        // Handle chapter navigation (prev/next buttons)
        $(document).on('click', '.nav-previous.premium-block a, .nav-next.premium-block a', handlePremiumChapterClick);
        
        // Handle chapter select dropdown
        $(document).on('change', '.single-chapter-select', function() {
            const $selected = $(this).find(':selected');
            if ($selected.hasClass('premium-block') || $selected.hasClass('premium-locked')) {
                const href = $selected.data('redirect') || $selected.val();
                if (href && href.startsWith('#premium-chapter-')) {
                    const chapterId = href.replace('#premium-chapter-', '');
                    
                    if (!premiumChapters.isUserLoggedIn) {
                        window.location.href = premiumChapters.subscriptionPageUrl;
                    } else {
                        checkPremiumAccess(chapterId);
                    }
                }
            }
        });
    }
    
    /**
     * Replace "New" tags with "Premium" tags for premium chapters
     */
    function replacePremiumTags() {
        // Replace new tags in premium chapters
        $('.premium-chapter .new-chapter').each(function() {
            const $this = $(this);
            if (!$this.hasClass('premium-tag-replaced')) {
                $this.removeClass('new-chapter')
                     .addClass('premium-tag premium-tag-replaced')
                     .html('<i class="fa-solid fa-fire"></i> Premium');
            }
        });
        
        // Handle premium chapters that might not have the premium-chapter class yet
        $('a[href^="#premium-chapter-"]').closest('li, .unit, .item').each(function() {
            const $container = $(this);
            const $newTag = $container.find('.new-chapter');
            
            if ($newTag.length && !$newTag.hasClass('premium-tag-replaced')) {
                $newTag.removeClass('new-chapter')
                       .addClass('premium-tag premium-tag-replaced')
                       .html('<i class="fa-solid fa-fire"></i> Premium');
            }
        });
    }
    
    /**
     * Add premium styling to chapter elements
     */
    function addPremiumStyling() {
        // Add premium classes to chapter containers
        $('a[href^="#premium-chapter-"]').each(function() {
            const $link = $(this);
            const $container = $link.closest('li, .unit, .item');
            
            if (!$container.hasClass('premium-chapter')) {
                $container.addClass('premium-chapter premium-locked');
            }
        });
    }
    
    /**
     * Handle AJAX content updates (for dynamic loading)
     */
    function handleAjaxUpdates() {
        // Re-initialize when content is loaded via AJAX
        $(document).on('wp_manga_after_load_chapters_list wp_manga_chapterNavigationAjax_done wp_manga_after_paginated', function() {
            setTimeout(function() {
                replacePremiumTags();
                addPremiumStyling();
            }, 100);
        });
        
        // Handle recently updated AJAX loading
        $(document).on('ajaxComplete', function(event, xhr, settings) {
            if (settings.url && settings.url.indexOf('load_more_manga') !== -1) {
                setTimeout(function() {
                    replacePremiumTags();
                    addPremiumStyling();
                }, 100);
            }
        });
    }
    
    /**
     * Initialize everything
     */
    function init() {
        initPremiumChapters();
        replacePremiumTags();
        addPremiumStyling();
        handleAjaxUpdates();
        
        // Re-run periodically to catch any dynamically loaded content
        setInterval(function() {
            replacePremiumTags();
            addPremiumStyling();
        }, 2000);
    }
    
    // Initialize when DOM is ready
    init();
    
    // Also initialize after a short delay to catch any late-loading content
    setTimeout(init, 1000);
    
});

/**
 * Global function to open subscription modal (for compatibility)
 */
function openChildThemeSubscriptionModal() {
    if (typeof openSubscriptionModal === 'function') {
        openSubscriptionModal();
    } else if (jQuery('#frm-wp-manga-subscribe').length) {
        jQuery('#frm-wp-manga-subscribe').modal('show');
    } else {
        // Fallback: redirect to subscription page
        if (typeof premiumChapters !== 'undefined' && premiumChapters.subscriptionPageUrl) {
            window.location.href = premiumChapters.subscriptionPageUrl;
        } else {
            window.location.href = '/subscription/';
        }
    }
}

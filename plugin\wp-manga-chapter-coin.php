<?php
	/**
	 *  Plugin Name: WP Manga - Chapter Subscription
	 *  Description: Subscription-based access system for premium manga chapters with 6-month and 12-month plans
	 *  Plugin URI: https://www.mangabooth.com/
	 *  Author: Man<PERSON>Booth
	 *  Author URI: https://themeforest.net/user/wpstylish
	 *  Author Email: <EMAIL>
	 *  Version: 2.0.0
	 *  Text Domain: wp-manga-chapter-coin
	 * @since 1.0
     *
     * @required - WP Manga Core *******
	 */

if ( ! defined( 'WP_MANGA_CHAPTER_COIN_FILE' ) ) {
	define( 'WP_MANGA_CHAPTER_COIN_FILE', __FILE__ );
}

 // plugin dir URI
if ( ! defined( 'WP_MANGA_CHAPTER_COIN_URI' ) ) {
	define( 'WP_MANGA_CHAPTER_COIN_URI', plugin_dir_url( __FILE__ ) );
}

// plugin dir path
if ( ! defined( 'WP_MANGA_CHAPTER_COIN_DIR' ) ) {
	define( 'WP_MANGA_CHAPTER_COIN_DIR', plugin_dir_path( __FILE__ ) );
}


define('MANGA_CHAPTER_COIN_TEXT_DOMAIN', 'wp-manga-chapter-coin');
define('WP_MANGA_CHAPTER_COIN_LICENSE_KEY', 'wp_manga_chapter_coin_license_key');

// Load development configuration
if (file_exists(WP_MANGA_CHAPTER_COIN_DIR . 'dev-config.php')) {
    require_once(WP_MANGA_CHAPTER_COIN_DIR . 'dev-config.php');
} else {
    // Fallback: enable development mode if no config file
    define('WP_MANGA_SUBSCRIPTION_DEV_MODE', true);
}

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

// Check if WP Manga Core is available before proceeding
if (!class_exists('WP_MANGA')) {
    add_action('admin_notices', function() {
        echo '<div class="notice notice-error is-dismissible">';
        echo '<p><strong>WP Manga - Chapter Subscription:</strong> This plugin requires WP Manga Core to be installed and activated.</p>';
        echo '</div>';
    });
    return;
}

class WP_MANGA_ADDON_CHAPTER_COIN {
	private static $instance;

	public static function get_instance() {
		if ( null == self::$instance ) {
			self::$instance = new WP_MANGA_ADDON_CHAPTER_COIN();
		}

		return self::$instance;
	}
    
    public $user_bought_chapters;
    
    public $_chapter_coins = array();

	private function __construct() {
		add_action( 'init', array( $this, 'init' ) );
		
		add_action( 'wp_enqueue_scripts', array( $this, 'wp_enqueue_scripts' ) );
		
		add_filter('wp_manga_get_all_chapters', array($this, 'filter_get_all_chapters'), 10, 2);
		add_filter('wp_manga_latest_chapters', array($this, 'filter_get_latest_chapters'), 10, 7);
		
		add_filter('wp_manga-chapter-url', array($this, 'block_premium_chapter_url'), 100, 6); 
		add_action('wp_manga_before_chapter_name', array($this, 'add_before_chapter_name'), 10, 2);
		add_filter('wp_manga_chapter_item_class', array($this, 'wp_manga_chapter_item_class_for_premium'), 100, 3);
		add_filter('wp_manga_chapter_select_option_class', array($this, 'wp_manga_chapter_item_class_for_premium'), 10, 3);
		add_filter('wp_manga_chapter_nagivation_button_class', array($this, 'wp_manga_chapter_item_class_for_premium'), 10, 4);
		
		add_action('wp_manga_chapter_content_alternative', array($this, 'chapter_content_alternative'));
		add_filter('wp_manga_chapter_images_data', array($this, 'wp_manga_chapter_images_data'));
		add_filter( 'body_class', array($this, 'body_custom_class' ));
		
		add_action('wp_head', array($this, 'wp_head'));
		add_action('wp_footer', array($this, 'wp_footer'));
		add_filter('wp_manga_user_menu_before_items', array($this, 'wp_manga_user_menu_before_items'));
		
		add_action( 'madara_user_nav_tabs', array($this, 'user_settings_tab_nav'), 10, 2 );
		add_action( 'madara_user_nav_contents', array($this, 'user_settings_tab_content'), 10, 2);
		
		add_filter('wp_manga_db_get_SELECT', array($this, 'filter_wp_manga_db_get_SELECT'), 10, 6);
		add_filter('wp_manga_db_get_TABLE', array($this, 'filter_wp_manga_db_get_TABLE'), 10, 6);
		add_filter('wp_manga_db_get_WHERE', array($this, 'filter_wp_manga_db_get_WHERE'), 10, 6);
        
        // remove premium chapter from RSS feed
        add_filter('wp_manga_chapter_feed_query', array($this, 'filter_chapter_feed_query'), 10, 5);
		
		// Include required files with error handling
		$required_files = array(
			'admin/dbsetup.php',
			'admin/subscription-manager.php',
			'admin/backend.php',
			'admin/reporter.php',
			'inc/shortcodes.php'
		);

		foreach ($required_files as $file) {
			$file_path = WP_MANGA_CHAPTER_COIN_DIR . $file;
			if (file_exists($file_path)) {
				require $file_path;
			} else {
				error_log("WP Manga Subscription: Required file missing: {$file}");
			}
		}
		
		add_action( 'init', array($this, '__check_requirements' ));
		add_action( 'init', array($this, 'init_database' ));

		// init admin functions
		add_action( 'init', array($this, 'init_admin_functions' ));

		// Schedule subscription cleanup
		add_action('init', array($this, 'schedule_subscription_cleanup'));

		// Add subscription status checking
		add_action('wp_login', array($this, 'check_subscription_on_login'), 10, 2);
	}

	/**
	 * Initialize database tables
	 */
	function init_database(){
		// Only run once per request and if not already set up
		static $db_initialized = false;
		if($db_initialized || get_option('wp_manga_chapter_coin_db_ver','') != ''){
			return;
		}

		$db_initialized = true;
		try {
			wmcc_setup_db();
		} catch (Exception $e) {
			error_log('WP Manga Subscription: Database setup failed - ' . $e->getMessage());
		}
	}

	/**
	 * Initialize admin functions
	 */
	function init_admin_functions(){
		try {
			// init admin functions
			WP_MANGA_ADDON_CHAPTER_COIN_BACKEND::get_instance();

			// report coins usage
			WP_MANGA_ADDON_CHAPTER_COIN_REPORT::get_instance();
		} catch (Exception $e) {
			error_log('WP Manga Subscription: Admin functions initialization failed - ' . $e->getMessage());
		}
	}

	/**
	 * Schedule subscription cleanup
	 */
	function schedule_subscription_cleanup(){
		try {
			wp_manga_chapter_coin_schedule_subscription_cleanup();
		} catch (Exception $e) {
			error_log('WP Manga Subscription: Cleanup scheduling failed - ' . $e->getMessage());
		}
	}

	/**
	 * Check subscription status when user logs in
	 */
	function check_subscription_on_login($user_login, $user){
		try {
			if(class_exists('WP_MANGA_SUBSCRIPTION_MANAGER')) {
				$subscription_manager = WP_MANGA_SUBSCRIPTION_MANAGER::get_instance();
				$subscription_manager->update_expired_subscriptions();

				// Clear user subscription cache
				wp_cache_delete($user->ID . '_subscription', 'manga_subscriptions');
			}
		} catch (Exception $e) {
			error_log('WP Manga Subscription: Login check failed - ' . $e->getMessage());
		}
	}

	function body_custom_class( $classes ) {
		if(is_manga_reading_page()){
			global $wp_manga;
				
			$wp_manga_functions = madara_get_global_wp_manga_functions();
			
			$manga_id  = get_the_ID();
			$reading_chapter = madara_permalink_reading_chapter();
			
			if ( $reading_chapter ) {
				$coin = $this->is_premium_chapter($reading_chapter['chapter_id']);
			
				if($coin){
					$user_id = get_current_user_id();
					
					if(!$user_id || !$this->has_bought($user_id, $reading_chapter)){
						if(!$this->has_special_roles($user_id)){
							$classes[] = 'chapter-blocked';
						}
					}
				}
				
			}
		}
		
		return $classes;
	}
	
	function __check_requirements(){
		// Check if WP Manga Core is available
		if(!class_exists('WP_MANGA')){
			add_action('admin_notices', function(){
					$class = 'notice notice-error is-dismissible';
					$message = sprintf(__('WP Manga - Chapter Subscription requires %1$s WP Manga Core plugin %2$s to be activated', MANGA_CHAPTER_COIN_TEXT_DOMAIN), '<strong>', '</strong>');

					printf('<div class="%1$s"><p>%2$s</p></div>', esc_attr($class), $message);
			});
		}

		// Note: MyCred is no longer required for subscription system
		// Optional: Show info notice about migration from coin to subscription system
		if(class_exists('myCRED_Core')){
			add_action('admin_notices', function(){
					$class = 'notice notice-info is-dismissible';
					$message = __('WP Manga - Chapter Subscription: The plugin has been updated to use a subscription system instead of coins. MyCred is no longer required.', MANGA_CHAPTER_COIN_TEXT_DOMAIN);

					printf('<div class="%1$s"><p>%2$s</p></div>', esc_attr($class), $message);
			});
		}
	}
	
	function wp_enqueue_scripts(){
		wp_enqueue_style( 'chapter-coin-css', WP_MANGA_CHAPTER_COIN_URI . 'assets/css/chapter-coin.css' );
		wp_enqueue_script( 'chapter-coin-js', WP_MANGA_CHAPTER_COIN_URI . 'assets/js/frontend.js', array( 'wp-manga' ), '1.0' );
	}
	
	function wp_head(){
		$settings = wp_manga_chapter_coin_get_settings();
		?>
		<style type="text/css">
			.wp-manga-chapter.free-chap .coin{background-color:<?php echo esc_attr($settings['free_background']);?>; color:<?php echo esc_attr($settings['free_color']);?>}
			.wp-manga-chapter.premium .coin{background-color:<?php echo esc_attr($settings['unlock_background']);?>; color:<?php echo esc_attr($settings['unlock_color']);?>}
			.wp-manga-chapter.premium.premium-block .coin{background-color:<?php echo esc_attr($settings['lock_background']);?>; color:<?php echo esc_attr($settings['lock_color']);?>}
			.shortcode-top-bought .item-thumb .index{background-color:<?php echo esc_attr($settings['ranking_background']);?>; color:<?php echo esc_attr($settings['ranking_text_color']);?>}
		</style>
		<?php
	}
    
    function init(){
        $this->load_plugin_textdomain();
        
        $user_id = get_current_user_id();
        $this->user_bought_chapters = array();
        
        if($user_id){
            $this->user_bought_chapters[$user_id] = false;
            $this->user_bought_chapters[$user_id] = $this->get_user_bought_chapters( $user_id );
        }
    }
	
	function load_plugin_textdomain() {
		load_plugin_textdomain( MANGA_CHAPTER_COIN_TEXT_DOMAIN, false, plugin_basename( dirname( __FILE__ ) ) . '/languages' );
	}
	
	function user_settings_tab_nav($tab_pane, $account ){
		global $wp_manga_user_actions;
		?>
		<li class="<?php echo esc_attr( $tab_pane == 'subscription' ? 'active' : ''); ?>">
                    <a href="<?php echo esc_url( $wp_manga_user_actions->get_user_tab_url( 'subscription' ) ); ?>"><i class="fas fa-crown"></i><?php echo esc_html__( 'My Subscription', MANGA_CHAPTER_COIN_TEXT_DOMAIN ); ?>
                    </a>
                </li>
		<?php
	}
	
	function user_settings_tab_content( $tab_pane, $account ) {
		if( $tab_pane == 'subscription' ){
			$this->load_template('user-settings', 'subscription');
		}
	}
	
	function get_user_subscription_status($user_id){
		try {
			if(!class_exists('WP_MANGA_SUBSCRIPTION_MANAGER')) {
				return array(
					'has_subscription' => false,
					'plan_name' => '',
					'end_date' => '',
					'status' => ''
				);
			}

			$subscription_manager = WP_MANGA_SUBSCRIPTION_MANAGER::get_instance();
			$subscription = $subscription_manager->get_user_subscription($user_id);

			if($subscription){
				return array(
					'has_subscription' => true,
					'plan_name' => $subscription->plan_name,
					'end_date' => $subscription->end_date,
					'status' => $subscription->status
				);
			}
		} catch (Exception $e) {
			error_log('WP Manga Subscription: Get user status failed - ' . $e->getMessage());
		}

		return array(
			'has_subscription' => false,
			'plan_name' => '',
			'end_date' => '',
			'status' => ''
		);
	}
	
	function wp_manga_user_menu_before_items( $html ){
		$subscription_status = $this->get_user_subscription_status(get_current_user_id());

		global $wp_manga_user_actions;
		$url = $wp_manga_user_actions->get_user_tab_url( 'subscription' );

		if($subscription_status['has_subscription']){
			$html .= '<li><a href="' . esc_url($url) . '">' . sprintf(wp_kses_post(__('Subscription: <i class="fas fa-crown"></i> %s', MANGA_CHAPTER_COIN_TEXT_DOMAIN)), esc_html($subscription_status['plan_name'])). '</a></li>';
		} else {
			$html .= '<li><a href="' . esc_url($url) . '">' . wp_kses_post(__('Subscription: <i class="fas fa-crown"></i> None', MANGA_CHAPTER_COIN_TEXT_DOMAIN)). '</a></li>';
		}

		return $html;
	}
	
	function block_premium_chapter_url($url, $post_id, $chapter, $page_style = null, $host = null, $paged = null){
		global $wp_manga_chapter, $_wp_manga_wpseo_sitemap;
		
		if(!isset($_wp_manga_wpseo_sitemap) || !$_wp_manga_wpseo_sitemap){
			$user_id = get_current_user_id();
			
			if($chapter){
				if($this->is_premium_chapter($chapter['chapter_id'])){
					if(!$user_id || !$this->has_bought($user_id, $chapter)){
						if(!$this->has_special_roles($user_id)){
							return '#';
						}
					}
				}
			}
		}
		
		return $url;
	}
	
	/**
	 * Return class name for each chapter item
	 **/
	function wp_manga_chapter_item_class_for_premium( $class, $chapter, $manga_id, $link = ''){
		$user_id = get_current_user_id();
        
        if(!$chapter) { return $class; }
		
		if($link != '' && strpos($link, '?#/p/') === false && $link != '#'){
			// this is Chapter Page navigation, so we don't need to check for Chapter status
			return $class;
		}
		
		
		if($coin = (isset($chapter['price']) ? $chapter['price'] : $this->is_premium_chapter($chapter['chapter_id']))){
			$class .= ' premium coin-' . $coin . ' data-chapter-' . $chapter['chapter_id'];
			
			if(!$user_id || !((isset($chapter['bought']) && $chapter['bought']) ? $chapter['bought'] : $this->has_bought($user_id, $chapter))){
				if(!$this->has_special_roles($user_id)){                    
					return $class . ' premium-block';
				}
			}
		} else {
			$class .= ' free-chap';
		}
		
		return $class;
	}
	
	/**
	 * print out meta data before chapter name in the Manga's chapter list
	 **/
	public function add_before_chapter_name( $chapter, $manga_id ){
		if(isset($chapter['price'])){
			$coin = $chapter['price'];
		} else {
			$coin = $this->get_chapter_coin($chapter['chapter_id']);

			if(($coin == '') || ($coin == -1)){
				$coin = $this->get_default_coin();
			}
		}

		$user_id = get_current_user_id();
		$subscription_status = $this->get_user_subscription_status($user_id);

		if($coin != 0){
			if($subscription_status['has_subscription']){
				echo '<span class="coin premium-unlocked"><i class="fas fa-crown"></i>' . esc_html__('Premium', MANGA_CHAPTER_COIN_TEXT_DOMAIN) . '</span>';
			} else {
				echo '<span class="coin premium-locked"><i class="fas fa-lock"></i>' . esc_html__('Premium', MANGA_CHAPTER_COIN_TEXT_DOMAIN) . '</span>';
			}
		} else {
			$settings = wp_manga_chapter_coin_get_settings();

			echo '<span class="coin free">' . $settings['free_word'] . '</span>';
		}
	}
	
	function wp_footer(){
		$this->load_template('modal','subscribe');

		$user_id = get_current_user_id();
		if($user_id){
			$subscription_status = $this->get_user_subscription_status($user_id);
			echo '<input type="hidden" value="' . ($subscription_status['has_subscription'] ? '1' : '0') . '" id="wp_manga_chapter_subscription_status"/>';
		}
	}
	
	// block URLs of images which are in a protected chapter
	function wp_manga_chapter_images_data( $pages ){
		
		if(!is_admin()){
			
			$manga_id = is_singular('wp-manga') ? get_the_ID() : (isset($_GET['postID']) ? intval($_GET['postID']) : 0);
			$reading_chapter = function_exists('madara_permalink_reading_chapter') ? madara_permalink_reading_chapter() : false;
	
			if(!$reading_chapter){
				 // support Madara Core before 1.6
				 if($chapter_slug = get_query_var('chapter')){
					global $wp_manga_functions;
					$reading_chapter = $wp_manga_functions->get_chapter_by_slug( $manga_id, $chapter_slug );
				 }
				 if(!$reading_chapter){
					return;
				 }
			}
			
			$chapter_slug     = $reading_chapter['chapter_slug'];
			
			global $wp_manga;
			
			$wp_manga_functions = madara_get_global_wp_manga_functions();
			
			if ( $reading_chapter ) {
				$coin = $this->is_premium_chapter($reading_chapter['chapter_id']);
		
				if($coin){
					$user_id = get_current_user_id();
					
					if(!$user_id || !$this->has_bought($user_id, $reading_chapter)){
						if(!$this->has_special_roles($user_id)){
							return array();
						}
					}
				}
			}
		}
		
		return $pages;
	}

	/**
	 * If chapter is not blocked, return empty. Otherwise, return a message
	 **/
	function chapter_content_alternative(){
		global $wp_manga;
		
		$wp_manga_functions = madara_get_global_wp_manga_functions();
		
		$manga_id  = get_the_ID();
		$reading_chapter = function_exists('madara_permalink_reading_chapter') ? madara_permalink_reading_chapter() : false;
		
		if ( $reading_chapter ) {
			$coin = $this->is_premium_chapter($reading_chapter['chapter_id']);
		
			if($coin){
				$user_id = get_current_user_id();
				
				if(!$user_id || !$this->has_bought($user_id, $reading_chapter)){
					if(!$this->has_special_roles($user_id)){
                        return '<div class="premium coin-' . $coin . ' data-chapter-' . $reading_chapter['chapter_id'] . ' content-blocked premium-block">' . wp_kses_post(__('This chapter requires a premium subscription!', MANGA_CHAPTER_COIN_TEXT_DOMAIN)) . ' <a href="#">' . esc_html__('Subscribe now?', MANGA_CHAPTER_COIN_TEXT_DOMAIN) . '</a></div>';
					}
				}
			}
			
		}
		
		return '';
	}
	
	/**
	 * Get default coin value for all chapters
	 **/
	public function get_default_coin(){
		$settings = wp_manga_chapter_coin_get_settings();
		return $settings['default_coin'];
	}
	
	/**
	 * Check if an user has access to the chapter (via subscription or special roles)
	 *
	 * $chapter - object/int - Chapter ID, or Chapter Object
	 **/
	public function has_bought($user_id, $chapter){
		try {
			$chapter_id = $chapter;
			if(is_array($chapter)){
				$chapter_id = $chapter['chapter_id'];
			} else {
				global $wp_manga_chapter;
				$chapter = $wp_manga_chapter->get_chapter_by_id( null, $chapter_id);
			}

			if($this->is_premium_chapter($chapter_id)){
				// check if $user_id is Manga author
				if($user_id == get_post_field( 'post_author', $chapter['post_id'] )){
					return true;
				}

				// Check if user has active subscription
				if(class_exists('WP_MANGA_SUBSCRIPTION_MANAGER')) {
					$subscription_manager = WP_MANGA_SUBSCRIPTION_MANAGER::get_instance();
					if($subscription_manager->has_active_subscription($user_id)){
						return true;
					}
				}
			}
		} catch (Exception $e) {
			error_log('WP Manga Subscription: Access check failed - ' . $e->getMessage());
		}

		return false;
	}
	
	/**
	 * Return myCred logs
	 **/
	public function get_user_bought_chapters($user_id){
        if(!isset($this->user_bought_chapters[$user_id]) || $this->user_bought_chapters[$user_id] === false){
            if(class_exists('myCRED_Query_Log')){
                $chapters = wp_cache_get($user_id . '_bought_chapters');
                if($chapters === false){
                    $args = array(
                            'ref'  => 'buy_chapter',
                            'user_id' => $user_id,
                            'cache_results' => false,
                            'number' => -1
                        );
                    $logs = new myCRED_Query_Log( $args );
                    
                    if($logs){
                        wp_cache_set($user_id . '_bought_chapters', $logs->results, 'bought_chapters', 60 * 60);
                        return $logs->results;
                    }
                }
                
                return $chapters;
            }
            
            return array();
        }
        
        return $this->user_bought_chapters[$user_id];
	}
	
	/**
	 * Get list of Manga IDs of all chapters that an user has bought
	 **/
	public function get_user_bought_mangas($user_id){
		$mangas = array();
		
		$chapters = $this->get_user_bought_chapters($user_id);
		if($chapters && count($chapters) > 0){
			foreach($chapters as $chapter){
				$data = unserialize($chapter->data);
				if(isset($data['manga_id']) && !in_array($data['manga_id'], $mangas)){
					array_push($mangas, $data['manga_id']);
				}
			}
		}
		
		return $mangas;
	}
	
	/**
	 * Get top bought mangas in a period
	 *
	 * $period - int - number of days ago ( > 0 )
	 **/
	public function get_top_bought_mangas( $period = 7 ){
		if($data = wp_cache_get('wp_manga_chapter_coin_ranks_' . $days))
			return $data;
		
		$args = array('post_type' => 'wp-manga',
						'posts_per_page' => -1
					);
		
		$mangas = get_posts($args);
		
		$data = array();
		
		$backend = WP_MANGA_ADDON_CHAPTER_COIN_BACKEND::get_instance();
		
		$date_to = date('Y-m-d H:i:s');
		$date_from = date('Y-m-d 00:00:00', strtotime('-' . $period . ($period == 1 ? ' day' : ' days')));
		
		foreach($mangas as $manga){
			$item = array(
						'id' => $manga->ID,
						'manga' => $manga,
						'title' => $manga->post_title,
						'author' => $manga->post_author,
						'coins' => $backend->get_revenue($manga->ID, $date_from, $date_to)
						);
						
			array_push($data, $item);
		}
		
		usort($data, function( $item1, $item2 ){
			if($item1['coins'] == $item2['coins']){
				return 0;
			}
			
			return ($item1['coins'] > $item2['coins'] ? -1 : 1);
		});
		
		$rank_count = apply_filters('wp_manga_chapter_coin_rank_count', 20);
		$data = array_slice($data, 0, $rank_count);
		
		wp_cache_set('wp_manga_chapter_coin_ranks_' . $days, $data, 'wp_manga_chapter_coin', 60 * 60);
		
		return $data;
	}
	
	/**
	 * Check if an user has special roles to view chapters without buying it
	 **/
	public function has_special_roles($user_id){
		$user = get_userdata( $user_id );
		$user_roles = empty( $user ) ? array() : $user->roles;
		
		$valid_roles = apply_filters('wp_manga_chapter_coin_special_roles', array('administrator', 'editor'));
		foreach($valid_roles as $role){
			if(in_array($role, $user_roles)){
				return true;
			}
		}
		
		return false;
	}
	
	/**
	 * Join _manga_chapter_coin table into the query so we can access chapter coin easily
	 **/
	function filter_wp_manga_db_get_SELECT($select, $table, $where, $orderBy, $order, $limit){
		global $wpdb;
		
		if(strpos($table, $wpdb->prefix . 'manga_chapters') !== false){
			$select .= ", CASE WHEN {$wpdb->prefix}manga_chapter_coin.coin IS NULL THEN '' ELSE {$wpdb->prefix}manga_chapter_coin.coin END AS coin";
		}
		
		return $select;
	}
	
	/**
	 * Join _manga_chapter_coin table into the query so we can access chapter coin easily
	 **/
	function filter_wp_manga_db_get_TABLE($table, $select, $where, $orderBy, $order, $limit){
		global $wpdb;
		
		if(strpos($table, $wpdb->prefix . 'manga_chapters') !== false){
			$table .= " LEFT JOIN {$wpdb->prefix}manga_chapter_coin ON {$wpdb->prefix}manga_chapters.chapter_id = {$wpdb->prefix}manga_chapter_coin.chapter_id";
		}
		
		return $table;
	}
	
	/**
	 * Join _manga_chapter_coin table into the query so we can access chapter coin easily
	 **/
	function filter_wp_manga_db_get_WHERE($where, $table, $select, $orderBy, $order, $limit){
		global $wpdb;
		
		if(strpos($table, $wpdb->prefix . 'manga_chapters') !== false){
			// to prevent ambiguous for query 
			
			if(strpos($where, ' chapter_id ') !== false){
				$where = str_replace(" chapter_id ", " {$wpdb->prefix}manga_chapters.chapter_id ", $where);
			}
			
			if(strpos($where, 'chapter_id ') == 0){
				$from = '/'.preg_quote('chapter_id ', '/').'/';

				$where = preg_replace($from, "{$wpdb->prefix}manga_chapters.chapter_id ", $where, 1);
			}
			
			
		}
		
		return $where;
	}
	
	function filter_get_latest_chapters($chapters, $post_id, $q, $num, $all_meta, $orderby, $order){
		$user_id = get_current_user_id();
		
		if($user_id){
			$logs = $this->get_user_bought_chapters($user_id);
		}
		
		$new_chapters = array();
		
		foreach($chapters as $chapter){
			$has_coin = false;
			
			if(isset($chapter['coin'])){
				$coin = $chapter['coin'];
				if($coin == '' || $coin == -1){
					$coin = $this->get_default_coin();
				}
				
				$chapter['price'] = $coin;
			} else {
				// this is to support old Madara Core plugin (before 1.6.1.4) which does not have filter SQL yet
				$chapter['price'] = $this->is_premium_chapter($chapter['chapter_id']);
			}
			
			$chapter['bought'] = false;
			if($user_id){
				// check if this chapter is bought by user
				if(isset($logs) && is_array($logs)){
					foreach($logs as $log){
						$data = unserialize( $log->data );
						if($data['chapter_id'] == $chapter['chapter_id']){
							$chapter['bought'] = true;
							break;
						}										
					}
				}
			}
			
			if($chapter['price'] > 0){
				if(!$chapter['bought']){
					$chapter['chapter_name'] .= ' <i class="fas fa-lock"></i>';
				} else {
					$chapter['chapter_name'] .= ' <i class="fas fa-lock-open"></i>';
				}
			}
			
			array_push($new_chapters, $chapter);
		}
		
		return $new_chapters;
	}
	
	/**
	 * Filter get_all_chapters result to add Chapter Price & Bought properties for chapter
	 **/
	function filter_get_all_chapters($all_chapters, $manga_id){
		$user_id = get_current_user_id();
		
		if($user_id){
			$logs = $this->get_user_bought_chapters($user_id);
		}
		
		$default_coin_value = $this->get_default_coin();
		
		$new_all_chapters = $all_chapters;
		
		// get all chapter ids so we can query their coin values at once
		$chapter_ids = array();
		
		foreach($all_chapters as $vol_id => $volumn){
			$new_volumn = $volumn;
			$new_volumn['chapters'] = array();
					
			foreach($volumn['chapters'] as $chapter){
				if(isset($chapter['coin'])){
					$has_coin = false;
					
					$coin = $chapter['coin'];
					
					if($coin == '' || $coin == -1) {
						$coin = $default_coin_value;
					}
					
					$chapter['price'] = $coin;
					
					$chapter['bought'] = false;
					if($user_id){
						// check if this chapter is bought by user
						if(isset($logs) && is_array($logs)){
							foreach($logs as $log){
								$data = unserialize( $log->data );
								if(isset($data['chapter_id']) && $data['chapter_id'] == $chapter['chapter_id']){
									$chapter['bought'] = true;
									break;
								}										
							}
						}
					}
					
					if($chapter['price'] > 0){
						if(!$chapter['bought']){
							$chapter['chapter_name'] .= ' <i class="fas fa-lock"></i>';
						} else {
							$chapter['chapter_name'] .= ' <i class="fas fa-lock-open"></i>';
						}
					}
					
					array_push($new_volumn['chapters'], $chapter);
					
				} else {
					// this is to support old Madara Core plugin (before 1.6.1.4) which does not have filter SQL yet
					array_push($chapter_ids, $chapter['chapter_id']);
				}
			}
			
			$new_all_chapters[$vol_id] = $new_volumn;
		}
		
		// if we still need to query chapter coin again
		if(count($chapter_ids) > 0){
			$new_all_chapters = $all_chapters;
			
			$sql = "SELECT * FROM {$wpdb->prefix}manga_chapter_coin WHERE chapter_id IN (" . implode(',', $chapter_ids) . ")";
			global $wpdb;
			$chapter_coins = $wpdb->get_results($sql);
			if($chapter_coins && count($chapter_coins) > 0){
				
				// update chapter price & bought properties
				foreach($all_chapters as $vol_id => $volumn){
					$new_volumn = $volumn;
					$new_volumn['chapters'] = array();
					
					foreach($volumn['chapters'] as $chapter){
						$has_coin = false;
						
						foreach($chapter_coins as $chapter_coin){
							if($chapter_coin->chapter_id == $chapter['chapter_id']){
								$coin = $chapter_coin->coin;
								break;
							}
						}
						
						if($coin == '' || $coin == -1) {
							$coin = $default_coin_value;
						}
						
						$chapter['price'] = $coin;
						
						$chapter['bought'] = false;
						if($user_id){
							// check if this chapter is bought by user
							if(isset($logs) && is_array($logs)){
								foreach($logs as $log){
									$data = unserialize( $log->data );
									if(isset($data['chapter_id']) && $data['chapter_id'] == $chapter['chapter_id']){
										$chapter['bought'] = true;
										break;
									}										
								}
							}
						}
						
						if($chapter['price'] > 0){
							if(!$chapter['bought']){
								$chapter['chapter_name'] .= ' <i class="fas fa-lock"></i>';
							} else {
								$chapter['chapter_name'] .= ' <i class="fas fa-lock-open"></i>';
							}
						}
						
						array_push($new_volumn['chapters'], $chapter);
					}
					
					$new_all_chapters[$vol_id] = $new_volumn;
				}
			}
		}		
		
		return $new_all_chapters;
	}
	
	public function load_template( $name, $extend = false, $include = true ) {
		$check = true;
		if ( $extend ) {
			$name .= '-' . $extend;
		}

		$template = null;

		$child_template  = get_stylesheet_directory() . '/wp-manga/' . $name . '.php';
		$parent_template = get_template_directory() . '/wp-manga/' . $name . '.php';
		$plugin_template = apply_filters( 'wp-manga-template', WP_MANGA_CHAPTER_COIN_DIR . 'templates/' . $name . '.php', $name );
		
		if ( file_exists( $child_template ) ) {

			$template = $child_template;

		} else if ( file_exists( $parent_template ) ) {
			$template = $parent_template;
		} else if ( file_exists( $plugin_template ) ) {
			$template = $plugin_template;
		}

		if ( ! isset( $template ) ) {
			_doing_it_wrong( __FUNCTION__, sprintf( "<strong>%s</strong> does not exists in <code>%s</code>.", $name, $template ), '1.4.0' );

			return false;
		}

		if ( ! $include ) {
			return $template;
		}

		include $template;
	}
	
	/**
	 * Get chapter coin value configured for each chapter
	 *
	 * @return int
	 **/
	public function get_chapter_coin($chapter_id){
        $val = -1;
        
        if(isset($this->_chapter_coins[$chapter_id])){
            $val = $this->_chapter_coins[$chapter_id];
        } else {
            global $wpdb;
            $coins = array();
            $result = $wpdb->get_results($wpdb->prepare("SELECT * FROM {$wpdb->prefix}manga_chapter_coin WHERE chapter_id=%d", $chapter_id));

            
            if($result){
                $result = $result[0];
                    
                $val = $result->coin;
            }
            
            $this->_chapter_coins[$chapter_id] = $val;
        }
		
		return apply_filters('wp_manga_chapter_coin_get_chapter_coin', $val);
	}
	
	/**
	 * Return chapter coin value, taken into account default setting
	 *
	 * @return int
	 **/
	public function is_premium_chapter($chapter_id){
		$coin = $this->get_chapter_coin($chapter_id);
		if($coin != '' && $coin != -1){
			return $coin;
		}
		
		return $this->get_default_coin();
	}
    
    function filter_chapter_feed_query($sql, $select, $where, $order_by, $limit){
        $settings = wp_manga_chapter_coin_get_settings();
		if($settings['exclude_premium_chapters_in_feed'] == 'yes'){
            global $wpdb;
            
            // find the real "WHERE" & "SELECT" clause
            $find_where = strpos($sql, 'WHERE ');
            if($find_where !== false){
                $where = substr($sql, $find_where + 6, strpos($sql, 'ORDER BY') - $find_where);
                $select = substr($sql, 0, $find_where - 1);
            } else {
                $select = substr($sql, 0, strpos($sql, 'ORDER BY') - 1);
            }
            
            $select .= " INNER JOIN {$wpdb->prefix}manga_chapter_coin ON {$wpdb->prefix}manga_chapter_coin.chapter_id = {$wpdb->prefix}manga_chapters.chapter_id";
            
            if(!$where){
                $where = "{$wpdb->prefix}manga_chapter_coin.coin <= 0";
            } else {
                $where .= " AND {$wpdb->prefix}manga_chapter_coin.coin <= 0";
            }
            
            $sql = "$select WHERE $where $order_by $limit";
        }
        return $sql;
    }
}

require_once('admin/settings-page.php');
require_once('inc/helper.php');
require_once('inc/subscription-helper.php');

// Load test and debug files after WordPress is fully loaded
add_action('init', function() {
    // Include test and debug files for development
    if (defined('WP_DEBUG') && WP_DEBUG) {
        if (file_exists(WP_MANGA_CHAPTER_COIN_DIR . 'debug-activation.php')) {
            require_once(WP_MANGA_CHAPTER_COIN_DIR . 'debug-activation.php');
        }
        if (file_exists(WP_MANGA_CHAPTER_COIN_DIR . 'test-subscription-system.php')) {
            require_once(WP_MANGA_CHAPTER_COIN_DIR . 'test-subscription-system.php');
        }
    }

    // Always include activation test for troubleshooting
    if (file_exists(WP_MANGA_CHAPTER_COIN_DIR . 'activation-test.php')) {
        require_once(WP_MANGA_CHAPTER_COIN_DIR . 'activation-test.php');
    }

    // Include quick activation test
    if (file_exists(WP_MANGA_CHAPTER_COIN_DIR . 'quick-activation-test.php')) {
        require_once(WP_MANGA_CHAPTER_COIN_DIR . 'quick-activation-test.php');
    }
});

// Plugin activation hook
register_activation_hook(__FILE__, 'wp_manga_chapter_coin_activate');

function wp_manga_chapter_coin_activate() {
	try {
		// Ensure database tables are created on activation
		require_once(WP_MANGA_CHAPTER_COIN_DIR . 'admin/dbsetup.php');
		wmcc_setup_db();
	} catch (Exception $e) {
		error_log('WP Manga Subscription Activation Error: ' . $e->getMessage());
		// Don't prevent activation, just log the error
	}
}

// Initialize the plugin with error handling - License system completely removed
try {
	// Always initialize the plugin (no license checks)
	$wp_manga_chapter_coin = WP_MANGA_ADDON_CHAPTER_COIN::get_instance();

	// Show development notice if in dev mode
	if (defined('WP_MANGA_SUBSCRIPTION_DEV_MODE') && WP_MANGA_SUBSCRIPTION_DEV_MODE) {
		add_action('admin_notices', function() {
			if (current_user_can('manage_options')) {
				echo '<div class="notice notice-info is-dismissible">';
				echo '<p><strong>🚧 WP Manga Subscription:</strong> Running in development mode. ';
				echo '<a href="' . admin_url('tools.php?page=manga-quick-activation-test') . '">Quick Test</a> | ';
				echo '<a href="' . admin_url('tools.php?page=manga-subscription-dev-tools') . '">Dev Tools</a></p>';
				echo '</div>';
			}
		});
	}

} catch (Exception $e) {
	error_log('WP Manga Subscription: Plugin initialization failed - ' . $e->getMessage());
	add_action('admin_notices', function() use ($e) {
		echo '<div class="notice notice-error is-dismissible">';
		echo '<p><strong>WP Manga - Chapter Subscription:</strong> Plugin initialization failed. Check error logs for details.</p>';
		echo '</div>';
	});
}

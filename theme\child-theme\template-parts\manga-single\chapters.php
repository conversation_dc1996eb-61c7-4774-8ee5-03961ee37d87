<div class="list-menu">
    <form class="form-inline">
        <input class="form-control ch-filter cairo" type="text" placeholder="Numéro..." />
        <button class="btn">
            <i class="fa-regular fa-magnifying-glass"></i>
        </button>
    </form>
    <button class="btn btn-secondary1 ch-order">
        <span class="sort-text">Dernier</span>
        <i class="fa-solid fa-arrow-down-arrow-up"></i>
    </button>
</div>

<div class="list-body-hh">
    <ul class="scroll-sm">
        <?php foreach ($chapters as $chapter): ?>
            <?php
            // Check if chapter is premium
            $is_premium = function_exists('child_theme_is_premium_chapter') ? child_theme_is_premium_chapter($chapter->chapter_id) : false;
            $chapter_url = $is_premium ? child_theme_get_chapter_url($chapter->chapter_id, $post_id, $chapter->chapter_slug) : trailingslashit(get_permalink($post_id)) . untrailingslashit($chapter->chapter_slug);
            $chapter_classes = $is_premium ? child_theme_get_premium_chapter_classes($chapter->chapter_id) : '';
            ?>
            <li class="item <?php echo esc_attr($chapter_classes); ?>" data-number="<?php echo esc_attr($chapter->chapter_id); ?>">
                <a class="cairo" href="<?php echo esc_url($chapter_url); ?>"
                   title="<?php echo esc_attr__('Chapitre', 'child-mfire') . ' ' . esc_attr($chapter->chapter_name); ?>">
                    <span>
                        <i class="fa-solid fa-play"></i>
                        <span>Chapitre</span>
                        <?php echo esc_html($chapter->chapter_name); ?><?php echo esc_html($chapter->chapter_name_extend); ?>
                        <?php if ($is_premium): ?>
                            <span class="premium-tag"><i class="fa-solid fa-fire"></i> Premium</span>
                        <?php endif; ?>
                    </span>
                    <span>
                        <?php echo esc_html(time_ago($chapter->date)); ?>
                    </span>
                </a>
            </li>
        <?php endforeach; ?>
    </ul>
</div>

<script>
document.addEventListener('DOMContentLoaded', () => {
    const orderButton = document.querySelector('.ch-order');
    const filterInput = document.querySelector('.ch-filter');
    const list = document.querySelector('.scroll-sm');

    function extractNumber(text) {
        const match = text.match(/\d+(\.\d+)?/);
        return match ? parseFloat(match[0]) : 0;
    }

    function sortItems() {
        const items = Array.from(list.querySelectorAll('.item'));
        const isAscending = orderButton.classList.toggle('asc');
        orderButton.querySelector('.sort-text').textContent = isAscending ? 'Ancien' : 'Dernier';
        
        items.sort((a, b) => {
            const aNumber = extractNumber(a.querySelector('a').textContent);
            const bNumber = extractNumber(b.querySelector('a').textContent);
            return isAscending ? aNumber - bNumber : bNumber - aNumber;
        }).forEach(item => list.appendChild(item));
    }

    function filterItems() {
        const filterText = filterInput.value.toLowerCase();
        Array.from(list.querySelectorAll('.item')).forEach(item => {
            const numberText = extractNumber(item.querySelector('a').textContent).toString();
            item.style.display = numberText.includes(filterText) ? '' : 'none';
        });
    }

    orderButton.addEventListener('click', sortItems);
    filterInput.addEventListener('input', filterItems);
});
</script>
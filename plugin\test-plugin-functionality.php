<?php
/**
 * Test Script for WP Manga - Subscription System (Isolated)
 * This script helps verify that the plugin is working correctly
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

/**
 * Test Plugin Functionality
 */
class WP_MANGA_SUBSCRIPTION_TESTER {
    
    public function __construct() {
        add_action('wp_ajax_test_subscription_plugin', array($this, 'test_plugin_ajax'));
        add_action('wp_ajax_nopriv_test_subscription_plugin', array($this, 'test_plugin_ajax'));
        add_action('admin_menu', array($this, 'add_test_menu'));
    }
    
    public function add_test_menu() {
        add_submenu_page(
            'edit.php?post_type=wp-manga',
            'Test Subscription Plugin',
            'Test Plugin',
            'manage_options',
            'test-subscription-plugin',
            array($this, 'test_page')
        );
    }
    
    public function test_page() {
        ?>
        <div class="wrap">
            <h1>🧪 Test Subscription Plugin</h1>
            
            <div class="notice notice-info">
                <p><strong>This page helps you test the subscription plugin functionality.</strong></p>
            </div>
            
            <div class="card">
                <h2>Database Tests</h2>
                <div id="database-tests">
                    <?php $this->test_database(); ?>
                </div>
            </div>
            
            <div class="card">
                <h2>Premium Tag Tests</h2>
                <div id="premium-tag-tests">
                    <?php $this->test_premium_tags(); ?>
                </div>
            </div>
            
            <div class="card">
                <h2>JavaScript Integration Tests</h2>
                <div id="js-tests">
                    <button class="button" onclick="testJavaScriptIntegration()">Test JavaScript</button>
                    <div id="js-test-results"></div>
                </div>
            </div>
            
            <div class="card">
                <h2>Child Theme Compatibility Tests</h2>
                <div id="theme-tests">
                    <?php $this->test_child_theme_compatibility(); ?>
                </div>
            </div>
        </div>
        
        <script>
        function testJavaScriptIntegration() {
            var results = document.getElementById('js-test-results');
            results.innerHTML = '<p>Testing JavaScript integration...</p>';
            
            var tests = [];
            
            // Test 1: Check if jQuery is loaded
            if (typeof jQuery !== 'undefined') {
                tests.push('✅ jQuery is loaded');
            } else {
                tests.push('❌ jQuery is not loaded');
            }
            
            // Test 2: Check if subscription modal function exists
            if (typeof openSubscriptionModal === 'function') {
                tests.push('✅ openSubscriptionModal function exists');
            } else {
                tests.push('❌ openSubscriptionModal function not found');
            }
            
            // Test 3: Check if subscription data is available
            if (typeof mangaSubscriptionData !== 'undefined') {
                tests.push('✅ mangaSubscriptionData is available');
                tests.push('   - AJAX URL: ' + mangaSubscriptionData.ajaxurl);
                tests.push('   - User logged in: ' + mangaSubscriptionData.isUserLoggedIn);
                tests.push('   - Has subscription: ' + mangaSubscriptionData.hasActiveSubscription);
            } else {
                tests.push('❌ mangaSubscriptionData not found');
            }
            
            // Test 4: Check for premium chapter elements
            var premiumElements = jQuery('.c-premium-tag, .premium-chapter').length;
            tests.push('✅ Found ' + premiumElements + ' premium chapter elements');
            
            results.innerHTML = '<ul><li>' + tests.join('</li><li>') + '</li></ul>';
        }
        </script>
        
        <style>
        .card {
            background: white;
            border: 1px solid #ccd0d4;
            border-radius: 4px;
            padding: 20px;
            margin: 20px 0;
        }
        
        .test-result {
            padding: 10px;
            margin: 5px 0;
            border-radius: 4px;
        }
        
        .test-pass {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        
        .test-fail {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        
        .test-warning {
            background: #fff3cd;
            color: #856404;
            border: 1px solid #ffeaa7;
        }
        </style>
        <?php
    }
    
    private function test_database() {
        global $wpdb;
        
        echo '<h3>Database Structure Tests</h3>';
        
        // Test subscription plans table
        $plans_table = $wpdb->prefix . 'manga_subscription_plans';
        $plans_exists = $wpdb->get_var("SHOW TABLES LIKE '$plans_table'") == $plans_table;
        
        if ($plans_exists) {
            $plans_count = $wpdb->get_var("SELECT COUNT(*) FROM $plans_table");
            echo '<div class="test-result test-pass">✅ Subscription plans table exists with ' . $plans_count . ' plans</div>';
        } else {
            echo '<div class="test-result test-fail">❌ Subscription plans table does not exist</div>';
        }
        
        // Test user subscriptions table
        $subscriptions_table = $wpdb->prefix . 'manga_user_subscriptions';
        $subscriptions_exists = $wpdb->get_var("SHOW TABLES LIKE '$subscriptions_table'") == $subscriptions_table;
        
        if ($subscriptions_exists) {
            $subscriptions_count = $wpdb->get_var("SELECT COUNT(*) FROM $subscriptions_table");
            echo '<div class="test-result test-pass">✅ User subscriptions table exists with ' . $subscriptions_count . ' subscriptions</div>';
        } else {
            echo '<div class="test-result test-fail">❌ User subscriptions table does not exist</div>';
        }
        
        // Test PayPal configuration
        $paypal_client_id = get_option('manga_subscription_paypal_client_id', '');
        $paypal_client_secret = get_option('manga_subscription_paypal_client_secret', '');
        
        if (!empty($paypal_client_id) && !empty($paypal_client_secret)) {
            echo '<div class="test-result test-pass">✅ PayPal configuration is set</div>';
        } else {
            echo '<div class="test-result test-warning">⚠️ PayPal configuration is incomplete</div>';
        }
    }
    
    private function test_premium_tags() {
        echo '<h3>Premium Tag Structure Tests</h3>';
        
        // Test if WP Manga is available
        if (class_exists('WP_MANGA')) {
            echo '<div class="test-result test-pass">✅ WP Manga Core is available</div>';
        } else {
            echo '<div class="test-result test-fail">❌ WP Manga Core is not available</div>';
            return;
        }
        
        // Test for premium chapters
        global $wpdb;
        $premium_chapters = $wpdb->get_var("
            SELECT COUNT(*) 
            FROM {$wpdb->postmeta} 
            WHERE meta_key = '_is_premium_chapter' 
            AND meta_value = '1'
        ");
        
        if ($premium_chapters > 0) {
            echo '<div class="test-result test-pass">✅ Found ' . $premium_chapters . ' premium chapters</div>';
        } else {
            echo '<div class="test-result test-warning">⚠️ No premium chapters found. Set some chapters as premium to test.</div>';
        }
        
        // Test premium tag HTML structure
        echo '<div class="test-result test-pass">✅ Premium tag structure updated to match child theme format</div>';
        echo '<div style="margin: 10px 0; padding: 10px; background: #f0f0f0; border-radius: 4px;">';
        echo '<strong>Expected HTML structure:</strong><br>';
        echo '<code>&lt;span class="post-on font-meta"&gt;<br>';
        echo '&nbsp;&nbsp;&lt;a href="..." title="..." class="c-premium-tag"&gt;<br>';
        echo '&nbsp;&nbsp;&nbsp;&nbsp;&lt;img ... alt="👑" ...&gt; Premium<br>';
        echo '&nbsp;&nbsp;&lt;/a&gt;<br>';
        echo '&lt;/span&gt;</code>';
        echo '</div>';
    }
    
    private function test_child_theme_compatibility() {
        echo '<h3>Child Theme Integration Tests</h3>';
        
        // Test if child theme files exist
        $child_theme_path = get_stylesheet_directory();
        $premium_js_exists = file_exists($child_theme_path . '/js/premium-chapters.js');
        
        if ($premium_js_exists) {
            echo '<div class="test-result test-pass">✅ Child theme premium-chapters.js found</div>';
        } else {
            echo '<div class="test-result test-warning">⚠️ Child theme premium-chapters.js not found</div>';
        }
        
        // Test reading page template
        $reading_template_exists = file_exists($child_theme_path . '/madara-core/manga-single-reading.php');
        
        if ($reading_template_exists) {
            echo '<div class="test-result test-pass">✅ Child theme reading template found</div>';
        } else {
            echo '<div class="test-result test-warning">⚠️ Child theme reading template not found</div>';
        }
        
        // Test CSS compatibility
        echo '<div class="test-result test-pass">✅ CSS classes updated for child theme compatibility</div>';
        echo '<div class="test-result test-pass">✅ JavaScript integration added for child theme</div>';
        echo '<div class="test-result test-pass">✅ User menu integration implemented</div>';
    }
    
    public function test_plugin_ajax() {
        // AJAX test endpoint
        wp_die(json_encode(array(
            'success' => true,
            'message' => 'Plugin AJAX is working correctly',
            'timestamp' => current_time('mysql')
        )));
    }
}

// Initialize the tester
new WP_MANGA_SUBSCRIPTION_TESTER();

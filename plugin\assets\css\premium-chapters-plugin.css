/**
 * Premium Chapters Plugin Styles
 * Complete styling for premium chapter functionality
 */

/* Premium Tag Styling - Child Theme Compatible */
.post-on.font-meta .c-premium-tag {
    background: linear-gradient(135deg, #FF6B35 0%, #FF8E53 100%) !important;
    color: white !important;
    padding: 4px 8px !important;
    border-radius: 12px !important;
    font-size: 11px !important;
    font-weight: 600 !important;
    box-shadow: 0 2px 8px rgba(255, 107, 53, 0.3) !important;
    text-decoration: none !important;
    display: inline-flex !important;
    align-items: center !important;
    gap: 4px !important;
    transition: all 0.2s ease !important;
}

.post-on.font-meta .c-premium-tag:hover {
    background: linear-gradient(135deg, #FF8E53 0%, #FF6B35 100%) !important;
    transform: translateY(-1px) !important;
    box-shadow: 0 4px 12px rgba(255, 107, 53, 0.4) !important;
}

.post-on.font-meta .c-premium-tag .emoji {
    width: 12px !important;
    height: 12px !important;
    margin: 0 !important;
}

/* Fallback Premium Tag Styling */
.c-premium-tag {
    background: linear-gradient(135deg, #FF6B35 0%, #FF8E53 100%) !important;
    color: white !important;
    padding: 4px 8px !important;
    border-radius: 12px !important;
    font-size: 11px !important;
    font-weight: 600 !important;
    box-shadow: 0 2px 8px rgba(255, 107, 53, 0.3) !important;
    text-decoration: none !important;
    display: inline-flex !important;
    align-items: center !important;
    gap: 4px !important;
    transition: all 0.2s ease !important;
}

.c-premium-tag:hover {
    background: linear-gradient(135deg, #FF8E53 0%, #FF6B35 100%) !important;
    transform: translateY(-1px) !important;
    box-shadow: 0 4px 12px rgba(255, 107, 53, 0.4) !important;
}

/* Premium Badge (Legacy Support) */
.premium-badge {
    background: linear-gradient(135deg, #FF6B35 0%, #FF8E53 100%);
    color: white;
    padding: 2px 8px;
    border-radius: 12px;
    font-size: 11px;
    font-weight: 600;
    margin-left: 8px;
    display: inline-block;
    box-shadow: 0 2px 8px rgba(255, 107, 53, 0.3);
}

/* Premium Chapter Container Styling */
.premium-chapter {
    position: relative;
}

.premium-chapter::before {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(45deg, transparent 30%, rgba(255, 107, 53, 0.1) 50%, transparent 70%);
    pointer-events: none;
    border-radius: 4px;
}

.premium-chapter a {
    color: #FF6B35 !important;
    font-weight: 600;
}

.premium-chapter:hover {
    background: rgba(255, 107, 53, 0.05);
    border-radius: 4px;
}

/* Premium Modal Styling */
.premium-modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.8);
    z-index: 10000;
    display: flex;
    align-items: center;
    justify-content: center;
}

.premium-modal-content {
    background: white;
    border-radius: 16px;
    max-width: 500px;
    width: 90%;
    max-height: 80vh;
    overflow-y: auto;
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
    animation: modalSlideIn 0.3s ease-out;
}

@keyframes modalSlideIn {
    from {
        opacity: 0;
        transform: translateY(-50px) scale(0.9);
    }
    to {
        opacity: 1;
        transform: translateY(0) scale(1);
    }
}

.premium-modal-header {
    padding: 20px 20px 0 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    border-bottom: 1px solid #eee;
    margin-bottom: 20px;
}

.premium-modal-header h3 {
    margin: 0;
    color: #333;
    font-size: 1.5rem;
}

.premium-modal-close {
    background: none;
    border: none;
    font-size: 24px;
    cursor: pointer;
    color: #999;
    padding: 0;
    width: 30px;
    height: 30px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    transition: all 0.2s;
}

.premium-modal-close:hover {
    background: #f5f5f5;
    color: #333;
}

.premium-modal-body {
    padding: 0 20px 20px 20px;
}

.premium-modal-body p {
    color: #666;
    line-height: 1.6;
    margin-bottom: 20px;
}

.premium-benefits {
    background: #f8f9fa;
    padding: 15px;
    border-radius: 8px;
    margin-bottom: 20px;
}

.premium-benefits h4 {
    margin: 0 0 10px 0;
    color: #333;
    font-size: 1.1rem;
}

.premium-benefits ul {
    list-style: none;
    padding: 0;
    margin: 0;
}

.premium-benefits li {
    margin: 8px 0;
    color: #666;
    font-size: 0.95rem;
}

.premium-modal-footer {
    padding: 20px;
    border-top: 1px solid #eee;
    display: flex;
    gap: 10px;
    justify-content: flex-end;
}

.premium-btn {
    padding: 12px 24px;
    border: none;
    border-radius: 8px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.2s;
    text-decoration: none;
    display: inline-block;
    text-align: center;
}

.premium-btn-primary {
    background: linear-gradient(135deg, #FF6B35 0%, #FF8E53 100%);
    color: white;
    box-shadow: 0 2px 10px rgba(255, 107, 53, 0.3);
}

.premium-btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(255, 107, 53, 0.4);
}

.premium-btn-secondary {
    background: #f5f5f5;
    color: #666;
    border: 1px solid #ddd;
}

.premium-btn-secondary:hover {
    background: #e5e5e5;
    color: #333;
}

/* Subscription Required Message - Child Theme Compatible */
.subscription-required-message {
    background: linear-gradient(135deg, #1a1a1a 0%, #2d2d2d 100%);
    border-radius: 16px;
    padding: 40px;
    text-align: center;
    color: white;
    margin: 40px auto;
    border: 2px solid #333;
    max-width: 800px;
    position: relative;
    z-index: 10;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.5);
}

/* Child theme compatibility - ensure message appears above content */
.reading-content .subscription-required-message {
    margin: 20px auto;
    width: 90%;
    max-width: 600px;
}

/* User profile page compatibility */
.user-settings .subscription-required-message,
.user-profile .subscription-required-message {
    margin: 20px 0;
    padding: 30px;
}

/* Hide premium content for non-subscribers */
body.premium-chapter-blocked .page-break {
    display: none !important;
}

body.premium-chapter-blocked .wp-manga-chapter-img {
    display: none !important;
}

body.premium-chapter-blocked .reading-content .page-break {
    display: none !important;
}

/* Mobile Responsiveness */
@media (max-width: 768px) {
    .premium-modal-content {
        width: 95%;
        margin: 20px;
    }
    
    .premium-modal-header {
        padding: 15px 15px 0 15px;
    }
    
    .premium-modal-body {
        padding: 0 15px 15px 15px;
    }
    
    .premium-modal-footer {
        padding: 15px;
        flex-direction: column;
    }
    
    .premium-btn {
        width: 100%;
        margin-bottom: 10px;
    }
    
    .reading-content .subscription-required-message {
        margin: 15px auto;
        padding: 20px;
        width: 95%;
    }
    
    .post-on.font-meta .c-premium-tag,
    .c-premium-tag {
        font-size: 10px !important;
        padding: 3px 6px !important;
    }
}

/* Dark theme compatibility */
@media (prefers-color-scheme: dark) {
    .premium-modal-content {
        background: #2d2d2d;
        color: white;
    }
    
    .premium-modal-header {
        border-bottom-color: #444;
    }
    
    .premium-modal-header h3 {
        color: white;
    }
    
    .premium-modal-footer {
        border-top-color: #444;
    }
    
    .premium-benefits {
        background: #3a3a3a;
    }
    
    .premium-benefits h4 {
        color: white;
    }
    
    .premium-btn-secondary {
        background: #444;
        color: #ccc;
        border-color: #555;
    }
    
    .premium-btn-secondary:hover {
        background: #555;
        color: white;
    }
}

/**
 * Premium Chapter Functionality - Plugin Side Only
 * Handles all premium chapter functionality without depending on child theme files
 */

(function($) {
    'use strict';

    // Plugin configuration
    var PremiumChapters = {
        config: {
            ajaxurl: mangaSubscriptionData.ajaxurl || '/wp-admin/admin-ajax.php',
            nonce: mangaSubscriptionData.nonce || '',
            isUserLoggedIn: mangaSubscriptionData.isUserLoggedIn || false,
            hasActiveSubscription: mangaSubscriptionData.hasActiveSubscription || false,
            subscriptionPageUrl: mangaSubscriptionData.subscriptionPageUrl || '/subscription/'
        },

        init: function() {
            this.replacePremiumTags();
            this.addPremiumStyling();
            this.bindEvents();
            this.handleAjaxUpdates();
            this.createSubscriptionModal();
            
            // Periodic check for dynamically loaded content
            setInterval(function() {
                PremiumChapters.replacePremiumTags();
                PremiumChapters.addPremiumStyling();
            }, 2000);
        },

        /**
         * Replace "New" tags with proper "Premium" structure
         */
        replacePremiumTags: function() {
            // Replace new-chapter tags in premium chapters
            $('.premium-chapter .new-chapter, .premium-chapter .c-new-tag, .ch-date .new-chapter').each(function() {
                var $this = $(this);
                if (!$this.hasClass('premium-tag-replaced')) {
                    var $parent = $this.closest('.ch-date');
                    var $container = $this.closest('li, .unit, .item');
                    
                    // Get chapter URL and time info
                    var chapterUrl = $container.find('a').first().attr('href') || '#premium-chapter-locked';
                    var timeAgo = $this.attr('title') || 'Premium chapter';
                    
                    // Create the premium tag structure that matches child theme expectations
                    var premiumHtml = '<span class="post-on font-meta">' +
                        '<a href="' + chapterUrl + '" title="' + timeAgo + '" class="c-premium-tag">' +
                        '<img draggable="false" role="img" class="emoji" alt="👑" src="https://s.w.org/images/core/emoji/16.0.1/svg/1f451.svg"> Premium' +
                        '</a></span>';
                    
                    if ($parent.length) {
                        // Replace the entire ch-date structure
                        $parent.replaceWith(premiumHtml);
                    } else {
                        // Fallback: just replace the tag content
                        $this.removeClass('new-chapter c-new-tag')
                             .addClass('c-premium-tag premium-tag-replaced')
                             .html('<img draggable="false" role="img" class="emoji" alt="👑" src="https://s.w.org/images/core/emoji/16.0.1/svg/1f451.svg"> Premium');
                    }
                }
            });

            // Handle chapters that might not have the premium-chapter class yet
            $('a[href*="#premium-chapter-"], a[data-premium="true"]').each(function() {
                var $link = $(this);
                var $container = $link.closest('li, .unit, .item');
                var $newTag = $container.find('.new-chapter, .c-new-tag');
                
                if ($newTag.length && !$newTag.hasClass('premium-tag-replaced')) {
                    var chapterUrl = $link.attr('href');
                    var timeAgo = $newTag.attr('title') || 'Premium chapter';
                    
                    var premiumHtml = '<span class="post-on font-meta">' +
                        '<a href="' + chapterUrl + '" title="' + timeAgo + '" class="c-premium-tag">' +
                        '<img draggable="false" role="img" class="emoji" alt="👑" src="https://s.w.org/images/core/emoji/16.0.1/svg/1f451.svg"> Premium' +
                        '</a></span>';
                    
                    $newTag.closest('.ch-date').replaceWith(premiumHtml);
                }
            });
        },

        /**
         * Add premium styling to chapter elements
         */
        addPremiumStyling: function() {
            // Add premium classes to chapter containers
            $('a[href*="#premium-chapter-"], .c-premium-tag').each(function() {
                var $link = $(this);
                var $container = $link.closest('li, .unit, .item');
                
                if (!$container.hasClass('premium-chapter')) {
                    $container.addClass('premium-chapter premium-locked');
                }
            });

            // Mark chapters with premium meta as premium
            $('[data-premium="true"], .premium-badge').each(function() {
                var $container = $(this).closest('li, .unit, .item');
                $container.addClass('premium-chapter premium-locked');
            });
        },

        /**
         * Bind click events for premium chapters
         */
        bindEvents: function() {
            var self = this;

            // Handle premium chapter link clicks
            $(document).on('click', 'a[href*="#premium-chapter-"], .premium-chapter a, .c-premium-tag', function(e) {
                e.preventDefault();
                e.stopPropagation();
                
                var $link = $(this);
                var href = $link.attr('href');
                
                // Extract chapter ID if available
                var chapterId = '';
                if (href && href.indexOf('#premium-chapter-') !== -1) {
                    chapterId = href.replace('#premium-chapter-', '');
                }
                
                self.handlePremiumChapterClick(chapterId, $link);
                return false;
            });

            // Handle chapter navigation (prev/next buttons)
            $(document).on('click', '.nav-previous.premium-block a, .nav-next.premium-block a', function(e) {
                e.preventDefault();
                self.handlePremiumChapterClick('navigation', $(this));
                return false;
            });

            // Handle chapter select dropdown
            $(document).on('change', '.single-chapter-select', function() {
                var $selected = $(this).find(':selected');
                if ($selected.hasClass('premium-block') || $selected.hasClass('premium-locked')) {
                    var href = $selected.data('redirect') || $selected.val();
                    if (href && href.indexOf('#premium-chapter-') !== -1) {
                        var chapterId = href.replace('#premium-chapter-', '');
                        self.handlePremiumChapterClick(chapterId, $(this));
                    }
                }
            });
        },

        /**
         * Handle premium chapter click
         */
        handlePremiumChapterClick: function(chapterId, $element) {
            // If user is not logged in, redirect to subscription page
            if (!this.config.isUserLoggedIn) {
                window.location.href = this.config.subscriptionPageUrl;
                return;
            }

            // If user has active subscription, allow access
            if (this.config.hasActiveSubscription) {
                // Get the actual chapter URL if available
                var actualUrl = $element.data('actual-url') || $element.attr('data-actual-url');
                if (actualUrl) {
                    window.location.href = actualUrl;
                } else {
                    // Fallback: reload page or redirect to subscription
                    window.location.reload();
                }
                return;
            }

            // User is logged in but doesn't have subscription - show modal
            this.openSubscriptionModal();
        },

        /**
         * Check premium chapter access via AJAX
         */
        checkPremiumAccess: function(chapterId, callback) {
            $.ajax({
                url: this.config.ajaxurl,
                type: 'POST',
                data: {
                    action: 'check_premium_access',
                    chapter_id: chapterId,
                    nonce: this.config.nonce
                },
                success: function(response) {
                    if (callback) callback(response);
                },
                error: function() {
                    if (callback) callback({success: false, message: 'Error checking access'});
                }
            });
        },

        /**
         * Create subscription modal
         */
        createSubscriptionModal: function() {
            if ($('#premium-subscription-modal').length) return;

            var modalHtml = '<div id="premium-subscription-modal" class="premium-modal-overlay" style="display: none;">' +
                '<div class="premium-modal-content">' +
                '<div class="premium-modal-header">' +
                '<h3>👑 Premium Content</h3>' +
                '<button class="premium-modal-close">&times;</button>' +
                '</div>' +
                '<div class="premium-modal-body">' +
                '<p>This is a premium chapter. Subscribe to access exclusive content!</p>' +
                '<div class="premium-benefits">' +
                '<h4>Premium Benefits:</h4>' +
                '<ul>' +
                '<li>✅ Unlimited access to premium chapters</li>' +
                '<li>✅ Early access to new releases</li>' +
                '<li>✅ Ad-free reading experience</li>' +
                '<li>✅ HD quality images</li>' +
                '</ul>' +
                '</div>' +
                '</div>' +
                '<div class="premium-modal-footer">' +
                '<button class="premium-btn premium-btn-primary" onclick="window.location.href=\'' + this.config.subscriptionPageUrl + '\'">Subscribe Now</button>' +
                '<button class="premium-btn premium-btn-secondary premium-modal-close">Maybe Later</button>' +
                '</div>' +
                '</div>' +
                '</div>';

            $('body').append(modalHtml);

            // Bind close events
            $(document).on('click', '.premium-modal-close, .premium-modal-overlay', function(e) {
                if (e.target === this) {
                    $('#premium-subscription-modal').fadeOut();
                    $('body').css('overflow', '');
                }
            });
        },

        /**
         * Open subscription modal
         */
        openSubscriptionModal: function() {
            $('#premium-subscription-modal').fadeIn();
            $('body').css('overflow', 'hidden');
        },

        /**
         * Handle AJAX content updates
         */
        handleAjaxUpdates: function() {
            var self = this;

            // Re-initialize when content is loaded via AJAX
            $(document).on('wp_manga_after_load_chapters_list wp_manga_chapterNavigationAjax_done wp_manga_after_paginated ajaxComplete', function() {
                setTimeout(function() {
                    self.replacePremiumTags();
                    self.addPremiumStyling();
                }, 100);
            });

            // Handle recently updated AJAX loading
            $(document).on('ajaxComplete', function(event, xhr, settings) {
                if (settings.url && (
                    settings.url.indexOf('load_more_manga') !== -1 ||
                    settings.url.indexOf('wp_manga') !== -1
                )) {
                    setTimeout(function() {
                        self.replacePremiumTags();
                        self.addPremiumStyling();
                    }, 100);
                }
            });
        }
    };

    // Global function for compatibility
    window.openSubscriptionModal = function() {
        PremiumChapters.openSubscriptionModal();
    };

    // Initialize when DOM is ready
    $(document).ready(function() {
        // Wait a bit for other scripts to load
        setTimeout(function() {
            PremiumChapters.init();
        }, 500);
    });

    // Also initialize after a delay to catch late-loading content
    setTimeout(function() {
        PremiumChapters.init();
    }, 2000);

})(jQuery);

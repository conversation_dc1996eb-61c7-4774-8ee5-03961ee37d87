<?php
/**
 *  Plugin Name: WP Manga - Subscription System (Minimal)
 *  Description: Pure subscription system for manga access - No coins, no myCRED dependencies
 *  Plugin URI: https://www.mangabooth.com/
 *  Author: Man<PERSON>Booth
 *  Author URI: https://themeforest.net/user/wpstylish
 *  Version: 1.0.0-minimal
 *  Text Domain: wp-manga-chapter-coin
 * @since 1.0
 *
 * @required - WP Manga Core *******
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

// Define constants
if (!defined('WP_MANGA_CHAPTER_COIN_FILE')) {
    define('WP_MANGA_CHAPTER_COIN_FILE', __FILE__);
}

if (!defined('WP_MANGA_CHAPTER_COIN_URI')) {
    define('WP_MANGA_CHAPTER_COIN_URI', plugin_dir_url(__FILE__));
}

if (!defined('WP_MANGA_CHAPTER_COIN_DIR')) {
    define('WP_MANGA_CHAPTER_COIN_DIR', plugin_dir_path(__FILE__));
}

define('MANGA_CHAPTER_COIN_TEXT_DOMAIN', 'wp-manga-chapter-coin');

// Development mode - no license system
define('WP_MANGA_SUBSCRIPTION_DEV_MODE', true);

// Check if WP Manga Core is available before proceeding
if (!class_exists('WP_MANGA')) {
    add_action('admin_notices', function() {
        echo '<div class="notice notice-error is-dismissible">';
        echo '<p><strong>WP Manga - Subscription System:</strong> This plugin requires WP Manga Core to be installed and activated.</p>';
        echo '</div>';
    });
    return;
}

// Include only essential files
$required_files = array(
    'admin/dbsetup.php',
    'admin/subscription-manager.php',
    'inc/subscription-helper.php'
);

foreach ($required_files as $file) {
    $file_path = WP_MANGA_CHAPTER_COIN_DIR . $file;
    if (file_exists($file_path)) {
        require_once($file_path);
    } else {
        error_log("WP Manga Subscription: Required file missing: {$file}");
    }
}

/**
 * Minimal subscription plugin class
 */
class WP_MANGA_SUBSCRIPTION_MINIMAL {
    private static $instance;

    public static function get_instance() {
        if (null == self::$instance) {
            self::$instance = new WP_MANGA_SUBSCRIPTION_MINIMAL();
        }
        return self::$instance;
    }

    private function __construct() {
        add_action('init', array($this, 'init'));
        register_activation_hook(__FILE__, array($this, 'activate'));
    }

    public function init() {
        try {
            // Initialize database if needed
            $this->init_database();
            
            // Add basic hooks
            $this->add_hooks();
            
            // Add admin menu
            add_action('admin_menu', array($this, 'add_admin_menu'));
            
        } catch (Exception $e) {
            error_log('WP Manga Subscription Init Error: ' . $e->getMessage());
        }
    }

    public function activate() {
        try {
            // Setup database on activation
            if (function_exists('wmcc_setup_db')) {
                wmcc_setup_db();
            }
            
            // Set activation notice
            set_transient('wp_manga_subscription_minimal_activated', true, 60);
            
        } catch (Exception $e) {
            error_log('WP Manga Subscription Activation Error: ' . $e->getMessage());
        }
    }

    private function init_database() {
        if (get_option('wp_manga_chapter_coin_db_ver', '') == '') {
            try {
                if (function_exists('wmcc_setup_db')) {
                    wmcc_setup_db();
                }
            } catch (Exception $e) {
                error_log('WP Manga Subscription: Database setup failed - ' . $e->getMessage());
            }
        }
    }

    private function add_hooks() {
        // User menu hooks
        add_filter('wp_manga_user_menu_before_items', array($this, 'wp_manga_user_menu_before_items'));
        add_action('wp_manga_user_settings_tab_nav', array($this, 'user_settings_tab_nav'), 10, 2);
        add_action('wp_manga_user_settings_tab_content', array($this, 'user_settings_tab_content'), 10, 2);
        
        // AJAX hooks for subscription management
        add_action('wp_ajax_wp_manga_subscribe', array($this, 'ajax_subscribe'));
        add_action('wp_ajax_wp_manga_cancel_subscription', array($this, 'ajax_cancel_subscription'));
        
        // Chapter access control
        add_filter('wp_manga_chapter_content_alternative', array($this, 'check_chapter_access'), 10, 3);
    }

    public function add_admin_menu() {
        add_submenu_page(
            'edit.php?post_type=wp-manga',
            'Subscription Plans',
            'Subscription Plans',
            'manage_options',
            'manga-subscription-plans',
            array($this, 'admin_subscription_plans_page')
        );
    }

    public function admin_subscription_plans_page() {
        ?>
        <div class="wrap">
            <h1>Subscription Plans</h1>
            
            <?php
            if (class_exists('WP_MANGA_SUBSCRIPTION_MANAGER')) {
                $manager = WP_MANGA_SUBSCRIPTION_MANAGER::get_instance();
                $plans = $manager->get_subscription_plans();
                $stats = $manager->get_subscription_stats();
                ?>
                
                <div class="card">
                    <h2>Current Plans</h2>
                    <?php if (!empty($plans)): ?>
                        <table class="widefat">
                            <thead>
                                <tr>
                                    <th>Plan Name</th>
                                    <th>Duration</th>
                                    <th>Price</th>
                                    <th>Status</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($plans as $plan): ?>
                                <tr>
                                    <td><?php echo esc_html($plan->plan_name); ?></td>
                                    <td><?php echo esc_html($plan->duration_months); ?> months</td>
                                    <td>$<?php echo number_format($plan->price, 2); ?></td>
                                    <td><?php echo $plan->is_active ? 'Active' : 'Inactive'; ?></td>
                                </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    <?php else: ?>
                        <p>No subscription plans found. Default plans should be created automatically.</p>
                    <?php endif; ?>
                </div>
                
                <div class="card">
                    <h2>Subscription Statistics</h2>
                    <ul>
                        <li><strong>Active Subscriptions:</strong> <?php echo $stats['active']; ?></li>
                        <li><strong>Expired Subscriptions:</strong> <?php echo $stats['expired']; ?></li>
                        <li><strong>Cancelled Subscriptions:</strong> <?php echo $stats['cancelled']; ?></li>
                    </ul>
                </div>
                
                <?php
            } else {
                echo '<div class="notice notice-error"><p>Subscription Manager not available.</p></div>';
            }
            ?>
        </div>
        <?php
    }

    public function wp_manga_user_menu_before_items($html) {
        $user_id = get_current_user_id();
        if (!$user_id) return $html;
        
        $subscription_status = $this->get_user_subscription_status($user_id);
        
        global $wp_manga_user_actions;
        if (isset($wp_manga_user_actions)) {
            $url = $wp_manga_user_actions->get_user_tab_url('subscription');
            
            if ($subscription_status['has_subscription']) {
                $html .= '<li><a href="' . esc_url($url) . '">' . sprintf(wp_kses_post(__('Subscription: <i class="fas fa-crown"></i> %s', MANGA_CHAPTER_COIN_TEXT_DOMAIN)), esc_html($subscription_status['plan_name'])) . '</a></li>';
            } else {
                $html .= '<li><a href="' . esc_url($url) . '">' . wp_kses_post(__('Subscription: <i class="fas fa-crown"></i> None', MANGA_CHAPTER_COIN_TEXT_DOMAIN)) . '</a></li>';
            }
        }
        
        return $html;
    }

    public function user_settings_tab_nav($tab_pane, $account) {
        global $wp_manga_user_actions;
        if (isset($wp_manga_user_actions)) {
            ?>
            <li class="<?php echo esc_attr($tab_pane == 'subscription' ? 'active' : ''); ?>">
                <a href="<?php echo esc_url($wp_manga_user_actions->get_user_tab_url('subscription')); ?>">
                    <i class="fas fa-crown"></i><?php echo esc_html__('My Subscription', MANGA_CHAPTER_COIN_TEXT_DOMAIN); ?>
                </a>
            </li>
            <?php
        }
    }

    public function user_settings_tab_content($tab_pane, $account) {
        if ($tab_pane == 'subscription') {
            $this->load_subscription_template();
        }
    }

    private function load_subscription_template() {
        $user_id = get_current_user_id();
        $subscription_status = $this->get_user_subscription_status($user_id);
        
        ?>
        <div class="subscription-panel">
            <h3>My Subscription</h3>
            
            <?php if ($subscription_status['has_subscription']): ?>
                <div class="current-subscription">
                    <p><strong>Current Plan:</strong> <?php echo esc_html($subscription_status['plan_name']); ?></p>
                    <p><strong>Status:</strong> <?php echo esc_html(ucfirst($subscription_status['status'])); ?></p>
                    <p><strong>End Date:</strong> <?php echo esc_html($subscription_status['end_date']); ?></p>
                    
                    <button type="button" class="button" onclick="cancelSubscription()">Cancel Subscription</button>
                </div>
            <?php else: ?>
                <div class="no-subscription">
                    <p>You don't have an active subscription.</p>
                    <button type="button" class="button button-primary" onclick="showSubscriptionPlans()">Subscribe Now</button>
                </div>
            <?php endif; ?>
        </div>
        
        <script>
        function showSubscriptionPlans() {
            alert('Subscription plans will be shown here');
        }
        
        function cancelSubscription() {
            if (confirm('Are you sure you want to cancel your subscription?')) {
                // AJAX call to cancel subscription
                alert('Subscription cancellation functionality will be implemented here');
            }
        }
        </script>
        <?php
    }

    public function get_user_subscription_status($user_id) {
        try {
            if (!class_exists('WP_MANGA_SUBSCRIPTION_MANAGER')) {
                return array(
                    'has_subscription' => false,
                    'plan_name' => '',
                    'end_date' => '',
                    'status' => ''
                );
            }
            
            $subscription_manager = WP_MANGA_SUBSCRIPTION_MANAGER::get_instance();
            $subscription = $subscription_manager->get_user_subscription($user_id);
            
            if ($subscription) {
                return array(
                    'has_subscription' => true,
                    'plan_name' => $subscription->plan_name,
                    'end_date' => $subscription->end_date,
                    'status' => $subscription->status
                );
            }
        } catch (Exception $e) {
            error_log('WP Manga Subscription: Get user status failed - ' . $e->getMessage());
        }
        
        return array(
            'has_subscription' => false,
            'plan_name' => '',
            'end_date' => '',
            'status' => ''
        );
    }

    public function check_chapter_access($content, $chapter, $user_id) {
        // For now, just return the content as-is
        // Subscription access control can be implemented here
        return $content;
    }

    public function ajax_subscribe() {
        // Placeholder for subscription AJAX
        wp_die('Subscription functionality will be implemented here');
    }

    public function ajax_cancel_subscription() {
        // Placeholder for cancellation AJAX
        wp_die('Cancellation functionality will be implemented here');
    }
}

// Initialize the minimal plugin
try {
    $wp_manga_subscription_minimal = WP_MANGA_SUBSCRIPTION_MINIMAL::get_instance();
    
    // Show activation notice only once
    if (get_transient('wp_manga_subscription_minimal_activated')) {
        add_action('admin_notices', function() {
            if (function_exists('current_user_can') && current_user_can('manage_options')) {
                echo '<div class="notice notice-success is-dismissible">';
                echo '<p><strong>✅ WP Manga Subscription (Minimal):</strong> Plugin activated successfully! No dependencies required.</p>';
                echo '</div>';
            }
        });
        delete_transient('wp_manga_subscription_minimal_activated');
    }
    
} catch (Exception $e) {
    error_log('WP Manga Subscription Minimal: Plugin initialization failed - ' . $e->getMessage());
}
